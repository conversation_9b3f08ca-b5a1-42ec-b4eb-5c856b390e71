package handlers

import (
	"fmt"
	"log"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
)

// ImportVulnerabilitiesRequest 导入漏洞请求
type ImportVulnerabilitiesRequest struct {
	File string `json:"file" binding:"required"`
}

// ImportVulnerabilities 导入漏洞
func (h *VulnerabilityHandler) ImportVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 处理文件上传
	file, err := c.FormFile("file")
	if err != nil {
		h.BadRequest(c, "请选择要上传的文件")
		return
	}

	// 检查文件类型
	if !strings.HasSuffix(strings.ToLower(file.Filename), ".xlsx") {
		h.BadRequest(c, "只支持Excel文件(.xlsx)")
		return
	}

	// 保存上传的文件
	uploadDir := "./uploads"
	if err := c.SaveUploadedFile(file, filepath.Join(uploadDir, file.Filename)); err != nil {
		h.InternalServerError(c, "保存文件失败: "+err.Error())
		return
	}

	// 解析Excel文件
	vulnerabilities, err := h.parseExcelFile(filepath.Join(uploadDir, file.Filename))
	if err != nil {
		h.InternalServerError(c, "解析Excel文件失败: "+err.Error())
		return
	}

	if len(vulnerabilities) == 0 {
		h.BadRequest(c, "Excel文件中没有有效的漏洞数据")
		return
	}

	// 批量导入漏洞
	successCount, failedCount, errors := h.batchImportVulnerabilities(vulnerabilities)

	// 返回导入结果
	result := map[string]interface{}{
		"total":        len(vulnerabilities),
		"successCount": successCount,
		"failedCount":  failedCount,
		"message":      fmt.Sprintf("导入完成，成功: %d，失败: %d", successCount, failedCount),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	h.Success(c, result)
}

// parseExcelFile 解析Excel文件
func (h *VulnerabilityHandler) parseExcelFile(filePath string) ([]models.Vulnerability, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("Excel文件中没有工作表")
	}

	// 读取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取Excel行失败: %v", err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("Excel文件中没有数据行")
	}

	var vulnerabilities []models.Vulnerability
	
	// 跳过表头，从第二行开始解析
	for i, row := range rows[1:] {
		if len(row) < 6 { // 至少需要6列基本数据
			log.Printf("第%d行数据不完整，跳过", i+2)
			continue
		}

		// 解析行数据
		vuln := models.Vulnerability{
			Name:           strings.TrimSpace(row[0]),
			VulnID:         strings.TrimSpace(row[1]),
			Severity:       strings.TrimSpace(row[2]),
			DisclosureDate: strings.TrimSpace(row[3]),
			Source:         strings.TrimSpace(row[4]),
			Description:    strings.TrimSpace(row[5]),
			CreatedAt:      time.Now().Unix(),
		}

		// 可选字段
		if len(row) > 6 {
			vuln.Tags = strings.TrimSpace(row[6])
		}
		if len(row) > 7 {
			vuln.Remediation = strings.TrimSpace(row[7])
		}
		if len(row) > 8 {
			vuln.References = strings.TrimSpace(row[8])
		}
		if len(row) > 9 {
			vuln.PushReason = strings.TrimSpace(row[9])
		}

		// 验证必填字段
		if vuln.Name == "" || vuln.VulnID == "" || vuln.Severity == "" || 
		   vuln.DisclosureDate == "" || vuln.Source == "" || vuln.Description == "" {
			log.Printf("第%d行必填字段不完整，跳过", i+2)
			continue
		}

		// 验证严重程度
		validSeverities := map[string]bool{
			"低危": true, "中危": true, "高危": true, "严重": true,
		}
		if !validSeverities[vuln.Severity] {
			log.Printf("第%d行严重程度无效: %s，跳过", i+2, vuln.Severity)
			continue
		}

		vulnerabilities = append(vulnerabilities, vuln)
	}

	return vulnerabilities, nil
}

// batchImportVulnerabilities 批量导入漏洞
func (h *VulnerabilityHandler) batchImportVulnerabilities(vulnerabilities []models.Vulnerability) (int, int, []string) {
	successCount := 0
	failedCount := 0
	var errors []string

	for i, vuln := range vulnerabilities {
		// 检查漏洞ID是否已存在
		var existingVuln models.Vulnerability
		if err := h.db.Where("vuln_id = ?", vuln.VulnID).First(&existingVuln).Error; err == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d行: 漏洞ID %s 已存在", i+2, vuln.VulnID))
			continue
		} else if err != gorm.ErrRecordNotFound {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d行: 检查漏洞ID失败: %v", i+2, err))
			continue
		}

		// 保存漏洞
		if err := h.db.Create(&vuln).Error; err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d行: 保存漏洞失败: %v", i+2, err))
			continue
		}

		successCount++

		// 尝试自动推送漏洞
		go h.autoTriggerPush(&vuln)
	}

	return successCount, failedCount, errors
}

// BatchImportVulnerabilitiesRequest 批量导入漏洞请求
type BatchImportVulnerabilitiesRequest struct {
	Vulnerabilities []CreateVulnerabilityRequest `json:"vulnerabilities" binding:"required"`
}

// BatchImportVulnerabilities 批量导入漏洞（JSON格式）
func (h *VulnerabilityHandler) BatchImportVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req BatchImportVulnerabilitiesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.Vulnerabilities) == 0 {
		h.BadRequest(c, "请提供要导入的漏洞数据")
		return
	}

	successCount := 0
	failedCount := 0
	var errors []string

	for i, vulnReq := range req.Vulnerabilities {
		// 检查漏洞ID是否已存在
		var existingVuln models.Vulnerability
		if err := h.db.Where("vuln_id = ?", vulnReq.VulnID).First(&existingVuln).Error; err == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d个漏洞: 漏洞ID %s 已存在", i+1, vulnReq.VulnID))
			continue
		} else if err != gorm.ErrRecordNotFound {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d个漏洞: 检查漏洞ID失败: %v", i+1, err))
			continue
		}

		// 创建漏洞对象
		vulnerability := models.Vulnerability{
			Name:           vulnReq.Name,
			VulnID:         vulnReq.VulnID,
			Severity:       vulnReq.Severity,
			Tags:           strings.Join(vulnReq.Tags, ","),
			DisclosureDate: vulnReq.DisclosureDate,
			Source:         vulnReq.Source,
			Description:    vulnReq.Description,
			References:     strings.Join(vulnReq.References, ","),
			Remediation:    vulnReq.Remediation,
			PushReason:     vulnReq.PushReason,
			CreatedAt:      time.Now().Unix(),
		}

		// 保存到数据库
		if err := h.db.Create(&vulnerability).Error; err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("第%d个漏洞: 保存失败: %v", i+1, err))
			continue
		}

		successCount++

		// 尝试自动推送漏洞
		go h.autoTriggerPush(&vulnerability)
	}

	// 返回导入结果
	result := map[string]interface{}{
		"total":        len(req.Vulnerabilities),
		"successCount": successCount,
		"failedCount":  failedCount,
		"message":      fmt.Sprintf("批量导入完成，成功: %d，失败: %d", successCount, failedCount),
	}

	if len(errors) > 0 {
		result["errors"] = errors
	}

	h.Success(c, result)
}
