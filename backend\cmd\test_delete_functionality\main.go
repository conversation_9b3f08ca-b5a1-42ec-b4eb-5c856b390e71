package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"net/http"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 1. 检查当前IOC情报
	fmt.Println("\n📊 检查当前IOC情报:")
	
	type IOCInfo struct {
		ID  uint   `json:"id"`
		IOC string `json:"ioc"`
	}
	
	var iocInfos []IOCInfo
	err = db.Table("ioc_intelligence").
		Select("id, ioc").
		Find(&iocInfos).Error
	
	if err != nil {
		log.Fatalf("查询IOC情报失败: %v", err)
	}
	
	fmt.Printf("当前IOC情报数量: %d\n", len(iocInfos))
	for _, ioc := range iocInfos {
		fmt.Printf("  ID:%d, IOC:%s\n", ioc.ID, ioc.IOC)
	}
	
	if len(iocInfos) == 0 {
		fmt.Println("没有IOC情报可以测试删除功能")
		return
	}
	
	// 2. 检查删除前的源数据状态
	fmt.Println("\n📊 删除前的源数据状态:")
	testIOC := iocInfos[0]
	
	var processedCount int64
	db.Table("ioc_source_data").
		Where("attack_ip = ? AND processed_status = ?", testIOC.IOC, "processed").
		Count(&processedCount)
	fmt.Printf("IOC %s 对应的已处理源数据: %d 条\n", testIOC.IOC, processedCount)
	
	// 3. 通过API删除IOC情报
	fmt.Printf("\n🗑️ 删除IOC情报 ID:%d, IOC:%s\n", testIOC.ID, testIOC.IOC)
	
	deleteURL := fmt.Sprintf("http://localhost:55555/api/ioc/intelligence-production/intelligence/%d", testIOC.ID)
	req, err := http.NewRequest("DELETE", deleteURL, nil)
	if err != nil {
		log.Fatalf("创建删除请求失败: %v", err)
	}
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("发送删除请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("读取响应失败: %v", err)
	}
	
	fmt.Printf("删除响应状态: %d\n", resp.StatusCode)
	fmt.Printf("删除响应内容: %s\n", string(body))
	
	// 4. 检查删除后的源数据状态
	fmt.Println("\n📊 删除后的源数据状态:")
	
	var unprocessedCount int64
	db.Table("ioc_source_data").
		Where("attack_ip = ? AND processed_status = ?", testIOC.IOC, "unprocessed").
		Count(&unprocessedCount)
	fmt.Printf("IOC %s 对应的未处理源数据: %d 条\n", testIOC.IOC, unprocessedCount)
	
	var processedCountAfter int64
	db.Table("ioc_source_data").
		Where("attack_ip = ? AND processed_status = ?", testIOC.IOC, "processed").
		Count(&processedCountAfter)
	fmt.Printf("IOC %s 对应的已处理源数据: %d 条\n", testIOC.IOC, processedCountAfter)
	
	// 5. 检查IOC情报是否被删除
	fmt.Println("\n📊 检查IOC情报是否被删除:")
	
	var remainingCount int64
	db.Table("ioc_intelligence").Where("id = ?", testIOC.ID).Count(&remainingCount)
	if remainingCount == 0 {
		fmt.Printf("✅ IOC情报 ID:%d 已成功删除\n", testIOC.ID)
	} else {
		fmt.Printf("❌ IOC情报 ID:%d 删除失败\n", testIOC.ID)
	}
	
	// 6. 检查是否可以重新生产
	fmt.Println("\n🔍 检查是否可以重新生产:")
	
	var readyCount int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&readyCount)
	fmt.Printf("满足生产条件且未处理: %d 条\n", readyCount)
	
	if readyCount > 0 {
		fmt.Println("🎉 删除重置功能正常！现在可以重新执行生产策略了。")
	} else {
		fmt.Println("⚠️ 删除重置功能可能有问题，没有找到可重新生产的数据。")
	}
}
