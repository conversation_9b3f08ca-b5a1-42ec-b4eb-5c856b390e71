#!/bin/bash

# 后端代码构建测试脚本

echo "=== 后端代码结构优化测试 ==="
echo ""

# 检查Go环境
echo "1. 检查Go环境..."
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装或不在PATH中"
    exit 1
fi

GO_VERSION=$(go version)
echo "✅ Go环境正常: $GO_VERSION"
echo ""

# 检查项目结构
echo "2. 检查项目结构..."
REQUIRED_DIRS=(
    "internal"
    "internal/config"
    "internal/database"
    "internal/models"
    "internal/service"
    "internal/handlers"
    "internal/middleware"
    "internal/utils"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ 目录存在: $dir"
    else
        echo "❌ 目录缺失: $dir"
        exit 1
    fi
done
echo ""

# 检查关键文件
echo "3. 检查关键文件..."
REQUIRED_FILES=(
    "main.go"
    "config.yaml"
    "go.mod"
    "internal/config/config.go"
    "internal/database/manager.go"
    "internal/models/models.go"
    "internal/service/interfaces.go"
    "internal/handlers/router.go"
    "internal/middleware/auth.go"
    "internal/utils/common.go"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ 文件存在: $file"
    else
        echo "❌ 文件缺失: $file"
        exit 1
    fi
done
echo ""

# 检查Go模块
echo "4. 检查Go模块依赖..."
if go mod verify; then
    echo "✅ Go模块验证通过"
else
    echo "❌ Go模块验证失败"
    exit 1
fi
echo ""

# 代码格式检查
echo "5. 检查代码格式..."
if gofmt -l . | grep -q .; then
    echo "❌ 代码格式不规范，需要运行 gofmt"
    echo "不规范的文件:"
    gofmt -l .
    exit 1
else
    echo "✅ 代码格式规范"
fi
echo ""

# 代码静态检查
echo "6. 进行代码静态检查..."
if command -v golint &> /dev/null; then
    LINT_OUTPUT=$(golint ./...)
    if [ -n "$LINT_OUTPUT" ]; then
        echo "⚠️  发现代码风格问题:"
        echo "$LINT_OUTPUT"
    else
        echo "✅ 代码风格检查通过"
    fi
else
    echo "⚠️  golint未安装，跳过代码风格检查"
fi
echo ""

# 编译测试
echo "7. 编译测试..."
if go build -o test_build main.go; then
    echo "✅ 编译成功"
    rm -f test_build
else
    echo "❌ 编译失败"
    exit 1
fi
echo ""

# 运行测试
echo "8. 运行单元测试..."
if go test ./...; then
    echo "✅ 单元测试通过"
else
    echo "⚠️  单元测试失败或无测试文件"
fi
echo ""

# 检查导入路径
echo "9. 检查导入路径..."
IMPORT_ISSUES=$(grep -r "vulnerability_push/" . --include="*.go" | grep -v "vulnerability_push/internal" | grep -v "vulnerability_push/crawlers" | grep -v "vulnerability_push/push" | grep -v "vulnerability_push/IOC_Feed" || true)
if [ -n "$IMPORT_ISSUES" ]; then
    echo "⚠️  发现可能的导入路径问题:"
    echo "$IMPORT_ISSUES"
else
    echo "✅ 导入路径检查通过"
fi
echo ""

# 检查配置文件
echo "10. 检查配置文件..."
if [ -f "config.yaml" ]; then
    echo "✅ 配置文件存在"
    # 这里可以添加YAML格式验证
else
    echo "❌ 配置文件缺失"
    exit 1
fi
echo ""

echo "=== 测试完成 ==="
echo "✅ 后端代码结构优化验证通过！"
echo ""
echo "下一步建议:"
echo "1. 运行应用进行功能测试"
echo "2. 检查数据库连接和迁移"
echo "3. 测试API接口功能"
echo "4. 完善单元测试覆盖率"
