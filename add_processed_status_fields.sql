-- 为IOC源数据表添加处理状态字段
-- 执行此脚本来修复IOC情报命中次数累计问题

-- 1. 添加处理状态字段
ALTER TABLE ioc_source_data 
ADD COLUMN processed_status VARCHAR(20) DEFAULT 'unprocessed' COMMENT '处理状态: unprocessed, processed';

-- 2. 添加处理时间字段
ALTER TABLE ioc_source_data 
ADD COLUMN processed_at BIGINT DEFAULT 0 COMMENT '处理时间';

-- 3. 为处理状态字段添加索引
ALTER TABLE ioc_source_data 
ADD INDEX idx_processed_status (processed_status);

-- 4. 为现有数据设置默认值
UPDATE ioc_source_data 
SET processed_status = 'unprocessed', processed_at = 0 
WHERE processed_status IS NULL OR processed_status = '';

-- 5. 查看迁移结果
SELECT 
    processed_status,
    COUNT(*) as count
FROM ioc_source_data 
GROUP BY processed_status;

-- 6. 显示表结构确认
DESCRIBE ioc_source_data;
