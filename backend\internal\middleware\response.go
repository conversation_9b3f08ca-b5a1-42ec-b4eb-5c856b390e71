package middleware

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// ResponseMiddleware 响应中间件
type ResponseMiddleware struct{}

// NewResponseMiddleware 创建响应中间件
func NewResponseMiddleware() *ResponseMiddleware {
	return &ResponseMiddleware{}
}

// StandardResponse 标准响应结构
type StandardResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"msg"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"requestId,omitempty"`
}

// CORS 跨域中间件
func (m *ResponseMiddleware) CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, Authorization, Content-Type, X-API-Key, X-Request-ID")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, Access-Control-Allow-Origin, X-Request-ID")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400") // 24小时

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestID 请求ID中间件
func (m *ResponseMiddleware) RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = fmt.Sprintf("%d", time.Now().UnixNano())
		}
		
		c.Set("RequestID", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// ErrorHandler 错误处理中间件
func (m *ResponseMiddleware) ErrorHandler() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID, _ := c.Get("RequestID")
		requestIDStr, _ := requestID.(string)

		var err string
		switch v := recovered.(type) {
		case error:
			err = v.Error()
		case string:
			err = v
		default:
			err = fmt.Sprintf("%v", v)
		}

		// 记录错误日志
		// TODO: 使用统一的日志记录器
		fmt.Printf("Panic recovered: %s, RequestID: %s\n", err, requestIDStr)

		c.JSON(http.StatusInternalServerError, StandardResponse{
			Code:      500,
			Message:   "服务器内部错误",
			Timestamp: time.Now().Unix(),
			RequestID: requestIDStr,
		})
	})
}

// ResponseWrapper 响应包装中间件
func (m *ResponseMiddleware) ResponseWrapper() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 如果已经有响应，不再处理
		if c.Writer.Written() {
			return
		}

		// 获取请求ID
		requestID, _ := c.Get("RequestID")
		requestIDStr, _ := requestID.(string)

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			c.JSON(http.StatusInternalServerError, StandardResponse{
				Code:      500,
				Message:   err.Error(),
				Timestamp: time.Now().Unix(),
				RequestID: requestIDStr,
			})
			return
		}

		// 如果没有响应内容，返回默认成功响应
		if c.Writer.Status() == http.StatusOK && !c.Writer.Written() {
			c.JSON(http.StatusOK, StandardResponse{
				Code:      200,
				Message:   "操作成功",
				Timestamp: time.Now().Unix(),
				RequestID: requestIDStr,
			})
		}
	}
}

// 响应辅助函数

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	requestID, _ := c.Get("RequestID")
	requestIDStr, _ := requestID.(string)

	c.JSON(http.StatusOK, StandardResponse{
		Code:      200,
		Message:   "操作成功",
		Data:      data,
		Timestamp: time.Now().Unix(),
		RequestID: requestIDStr,
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	requestID, _ := c.Get("RequestID")
	requestIDStr, _ := requestID.(string)

	c.JSON(http.StatusOK, StandardResponse{
		Code:      200,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
		RequestID: requestIDStr,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	requestID, _ := c.Get("RequestID")
	requestIDStr, _ := requestID.(string)

	c.JSON(code, StandardResponse{
		Code:      code,
		Message:   message,
		Timestamp: time.Now().Unix(),
		RequestID: requestIDStr,
	})
}

// BadRequest 400错误响应
func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, message)
}

// Unauthorized 401错误响应
func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, message)
}

// Forbidden 403错误响应
func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, message)
}

// NotFound 404错误响应
func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, message)
}

// InternalServerError 500错误响应
func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, message)
}

// ValidationError 验证错误响应
func ValidationError(c *gin.Context, err error) {
	BadRequest(c, "参数验证失败: "+err.Error())
}

// PaginatedSuccess 分页成功响应
func PaginatedSuccess(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	requestID, _ := c.Get("RequestID")
	requestIDStr, _ := requestID.(string)

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	response := gin.H{
		"list": data,
		"pagination": gin.H{
			"total":       total,
			"page":        page,
			"pageSize":    pageSize,
			"totalPages":  totalPages,
			"hasNext":     page < totalPages,
			"hasPrevious": page > 1,
		},
	}

	c.JSON(http.StatusOK, StandardResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      response,
		Timestamp: time.Now().Unix(),
		RequestID: requestIDStr,
	})
}

// FileResponse 文件响应
func FileResponse(c *gin.Context, filename string, data []byte) {
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", fmt.Sprintf("%d", len(data)))
	c.Data(http.StatusOK, "application/octet-stream", data)
}

// StreamResponse 流式响应
func StreamResponse(c *gin.Context, contentType string, data []byte) {
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", fmt.Sprintf("%d", len(data)))
	c.Data(http.StatusOK, contentType, data)
}
