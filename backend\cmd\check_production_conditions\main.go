package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查各种条件的数据分布
	fmt.Println("\n📊 数据条件分布:")
	
	// 攻击次数 >= 3
	var attackCount3 int64
	db.Table("ioc_source_data").Where("attack_count >= ?", 3).Count(&attackCount3)
	fmt.Printf("攻击次数 >= 3: %d 条\n", attackCount3)
	
	// 威胁评分 >= 2
	var threatScore2 int64
	db.Table("ioc_source_data").Where("threat_score >= ?", 2).Count(&threatScore2)
	fmt.Printf("威胁评分 >= 2: %d 条\n", threatScore2)
	
	// 同时满足两个条件
	var bothConditions int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ?", 3, 2).
		Count(&bothConditions)
	fmt.Printf("同时满足两个条件: %d 条\n", bothConditions)
	
	// 同时满足两个条件且未处理
	var bothConditionsUnprocessed int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&bothConditionsUnprocessed)
	fmt.Printf("同时满足两个条件且未处理: %d 条\n", bothConditionsUnprocessed)
	
	// 显示满足攻击次数条件的数据
	fmt.Println("\n📋 攻击次数 >= 3 的数据:")
	
	type SourceData struct {
		ID              uint    `json:"id"`
		AttackIP        string  `json:"attack_ip"`
		VictimIP        string  `json:"victim_ip"`
		AttackCount     int     `json:"attack_count"`
		ThreatScore     float64 `json:"threat_score"`
		ProcessedStatus string  `json:"processed_status"`
		Category        string  `json:"category"`
	}
	
	var attackSamples []SourceData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Where("attack_count >= ?", 3).
		Find(&attackSamples)
	
	for _, sample := range attackSamples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
	
	// 显示威胁评分 >= 2 的数据（前10条）
	fmt.Println("\n📋 威胁评分 >= 2 的数据（前10条）:")
	
	var threatSamples []SourceData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Where("threat_score >= ?", 2).
		Limit(10).
		Find(&threatSamples)
	
	for _, sample := range threatSamples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
	
	// 检查已处理的数据
	fmt.Println("\n📋 已处理的数据:")
	
	var processedSamples []SourceData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Where("processed_status = ?", "processed").
		Find(&processedSamples)
	
	for _, sample := range processedSamples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
}
