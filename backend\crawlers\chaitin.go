package crawlers

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

// 长亭漏洞库采集器
type ChaitinCrawler struct {
	client *resty.Client
	debug  bool
}

// 创建长亭漏洞库采集器
func NewChaitinCrawler() Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Referer", "https://stack.chaitin.com/vuldb/index")
	client.SetHeader("Origin", "https://stack.chaitin.com")
	client.SetHeader("Accept", "application/json, text/plain, */*")
	client.SetTimeout(30 * time.Second)

	return &ChaitinCrawler{
		client: client,
	}
}

// 创建调试模式的长亭漏洞库采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewChaitinCrawlerWithDebug() *ChaitinCrawler {
	client := resty.New()
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "application/json, text/plain, */*")
	client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	client.SetHeader("Origin", "https://stack.chaitin.com")
	client.SetHeader("Referer", "https://stack.chaitin.com/vuldb")
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)
	client.SetTimeout(30 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// client.SetDebug(true)

	return &ChaitinCrawler{
		client: client,
		debug:  true,
	}
}

// 获取采集源信息
func (c *ChaitinCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "chaitin",
		DisplayName: "长亭漏洞库",
		Link:        "https://stack.chaitin.com/vuldb/index",
	}
}

// 获取最新漏洞信息
func (c *ChaitinCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	var results []*VulnInfo

	// 更新API请求路径
	urlTpl := "https://stack.chaitin.com/api/v2/vuln/list/?limit=%d&offset=%d&search="

	logger.Infof("长亭漏洞库采集器开始执行，将采集 %d 页数据", pageLimit)

	limit := 15 // 每页15条数据
	for i := 0; i < pageLimit; i++ {
		offset := i * limit
		url := fmt.Sprintf(urlTpl, limit, offset)

		logger.Infof("正在请求长亭API，第 %d 页", i+1)

		var body ChaitinResp
		resp, err := c.client.R().
			SetResult(&body).
			SetContext(ctx).
			Get(url)

		if err != nil {
			logger.Errorf("长亭API请求失败: %v", err)
			return nil, err
		}

		logger.Infof("长亭API响应状态码: %d", resp.StatusCode())

		// 检查API响应是否成功
		if resp.StatusCode() != 200 || body.Code != 0 || body.Data.List == nil {
			logger.Errorf("长亭API响应异常: Code=%d, Message=%s", body.Code, body.Msg)
			continue
		}

		logger.Infof("成功获取长亭漏洞数据，共 %d 条", len(body.Data.List))

		for _, d := range body.Data.List {
			severity := SeverityLow
			switch d.Severity {
			case "low":
				severity = SeverityLow
			case "medium":
				severity = SeverityMedium
			case "high":
				severity = SeverityHigh
			case "critical":
				severity = SeverityCritical
			}

			// 使用披露日期（如果有），否则使用创建时间
			var disclosureDate string
			if d.DisclosureDate != "" {
				disclosureDate = d.DisclosureDate
			} else {
				// 格式化时间为YYYY-MM-DD
				t, err := time.Parse(time.RFC3339, d.CreatedAt)
				if err == nil {
					disclosureDate = t.Format("2006-01-02")
				}
			}

			// 日期范围筛选
			if (startDate != "" || endDate != "") && disclosureDate != "" {
				vulnDate, err := time.Parse("2006-01-02", disclosureDate)
				if err == nil {
					// 检查开始日期
					if startDate != "" {
						startDateTime, err := time.Parse("2006-01-02", startDate)
						if err == nil && vulnDate.Before(startDateTime) {
							continue // 漏洞日期早于开始日期，跳过
						}
					}

					// 检查结束日期
					if endDate != "" {
						endDateTime, err := time.Parse("2006-01-02", endDate)
						if err == nil && vulnDate.After(endDateTime) {
							continue // 漏洞日期晚于结束日期，跳过
						}
					}
				}
			}

			// 获取漏洞详情
			detailInfo, err := c.getVulnDetail(ctx, d.Id)
			if err != nil {
				logger.Errorf("获取漏洞详情失败: %v", err)
				// 即使获取详情失败，也继续处理
			}

			// 处理参考链接
			var refs []string
			if d.References != "" {
				refs = strings.Split(d.References, "\n")
			} else if detailInfo != nil && detailInfo.References != "" {
				refs = strings.Split(detailInfo.References, "\n")
			}

			// 处理CVE ID
			cveId := d.CveId

			// 处理修复建议
			var remediation string
			if detailInfo != nil {
				remediation = detailInfo.FixSteps
			}

			// 处理标签
			var tags []string
			if cveId != "" {
				tags = append(tags, cveId)
			}

			// 添加CNVD、CNNVD编号作为标签
			if d.CnnvdId != "" {
				tags = append(tags, d.CnnvdId)
			}
			if d.CnvdId != "" {
				tags = append(tags, d.CnvdId)
			}

			// 添加漏洞类型标签
			if d.Weakness != "" {
				tags = append(tags, d.Weakness)
			}

			info := &VulnInfo{
				UniqueKey:   d.CtId,
				Title:       d.Title,
				Description: d.Summary,
				Severity:    severity,
				CVE:         cveId,
				Disclosure:  disclosureDate,
				References:  refs,
				From:        "https://stack.chaitin.com/vuldb/detail/" + d.Id,
				Tags:        tags,
				Remediation: remediation,
			}
			results = append(results, info)
		}

		// 检查是否还有下一页
		if body.Data.Next == "" {
			logger.Warnf("没有更多页面，采集结束")
			break
		}

		// 添加延迟，避免请求过快
		time.Sleep(time.Duration(1000+time.Now().Nanosecond()%1000) * time.Millisecond)
	}

	// 如果没有获取到数据，直接返回空结果
	if len(results) == 0 {
		logger.Warnf("未获取到长亭漏洞数据，返回空结果")
		return []*VulnInfo{}, nil
	} else {
		logger.Infof("成功获取长亭漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (c *ChaitinCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	var results []*VulnInfo

	// 更新API请求路径
	urlTpl := "https://stack.chaitin.com/api/v2/vuln/list/?limit=%d&offset=%d&search="

	logger.Infof("长亭漏洞库采集器开始执行，将采集 %d 页数据", pageLimit)

	limit := 15 // 每页15条数据

	// 获取所有页面的漏洞列表
	var allVulns []ChaitinVuln
	var apiErrorCount int = 0
	
	for i := 0; i < pageLimit; i++ {
		offset := i * limit
		url := fmt.Sprintf(urlTpl, limit, offset)

		logger.Infof("正在请求长亭API，第 %d 页", i+1)

		var body ChaitinResp
		resp, err := c.client.R().
			SetResult(&body).
			SetContext(ctx).
			Get(url)

		if err != nil {
			logger.Errorf("长亭API请求失败: %v", err)
			return nil, err
		}

		logger.Infof("长亭API响应状态码: %d", resp.StatusCode())

		// 检查API响应是否成功
		if resp.StatusCode() != 200 || body.Code != 0 || body.Data.List == nil {
			apiErrorCount++
			
			// 提取简洁的错误信息
			var errorDetails string
			if body.Msg != "" {
				errorDetails = body.Msg
			} else if resp.StatusCode() != 200 {
				// 只显示状态码，不显示完整响应内容
				errorDetails = fmt.Sprintf("HTTP状态码:%d", resp.StatusCode())
			} else {
				// 简化错误信息
				errorDetails = fmt.Sprintf("响应code=%d", body.Code)
			}
			
			errMsg := fmt.Sprintf("长亭API响应异常: Code=%d, Message=%s", body.Code, errorDetails)
			logger.Errorf(errMsg)
			
			// 如果连续出现API错误，则返回错误
			if apiErrorCount >= 2 {
				return nil, fmt.Errorf(errMsg)
			}
			continue
		}

		logger.Infof("成功获取长亭漏洞数据，共 %d 条", len(body.Data.List))

		allVulns = append(allVulns, body.Data.List...)

		// 如果没有更多数据，提前结束
		if body.Data.Next == "" {
			logger.Warnf("没有更多数据，采集结束")
			break
		}

		// 添加延迟，避免请求过快
		time.Sleep(time.Duration(1000+time.Now().Nanosecond()%1000) * time.Millisecond)
	}

			if len(allVulns) == 0 {
		logger.Warnf("未获取到长亭漏洞数据")
		// 如果API请求成功但没有获取到数据，返回空结果
		if apiErrorCount > 0 {
			// 使用简洁的错误信息
			return nil, fmt.Errorf("长亭漏洞库API响应异常")
		}
		return []*VulnInfo{}, nil
	}

	// 筛选出不存在于数据库中的漏洞
	var newVulns []ChaitinVuln
	var existingCount int
	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.CtId) {
			existingCount++
			//logger.Infof("漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.CtId)
			continue
		}
		newVulns = append(newVulns, vuln)
	}

	logger.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条需要获取详情", existingCount, len(newVulns))

	// 处理日期过滤
	hasDateFilter := startDate != "" || endDate != ""
	var startDateTime, endDateTime time.Time
	var startDateErr, endDateErr error

	if startDate != "" {
		startDateTime, startDateErr = time.Parse("2006-01-02", startDate)
		if startDateErr != nil {
			logger.Errorf("无效的开始日期格式: %s, %v", startDate, startDateErr)
		}
	}

	if endDate != "" {
		endDateTime, endDateErr = time.Parse("2006-01-02", endDate)
		if endDateErr != nil {
			logger.Errorf("无效的结束日期格式: %s, %v", endDate, endDateErr)
		}
	}

	// 处理筛选后的漏洞
	for _, vuln := range newVulns {
		// 使用披露日期（如果有），否则使用创建时间
		var disclosureDate string
		if vuln.DisclosureDate != "" {
			disclosureDate = vuln.DisclosureDate
		} else {
			// 格式化时间为YYYY-MM-DD
			t, err := time.Parse(time.RFC3339, vuln.CreatedAt)
			if err == nil {
				disclosureDate = t.Format("2006-01-02")
			}
		}

		// 应用日期过滤
		if hasDateFilter && disclosureDate != "" {
			vulnDate, dateErr := time.Parse("2006-01-02", disclosureDate)
			if dateErr == nil {
				// 检查开始日期
				if startDate != "" && startDateErr == nil && vulnDate.Before(startDateTime) {
					continue // 漏洞日期早于开始日期，跳过
				}

				// 检查结束日期
				if endDate != "" && endDateErr == nil && vulnDate.After(endDateTime) {
					continue // 漏洞日期晚于结束日期，跳过
				}
			}
		}

		// 获取漏洞详情
		detailInfo, err := c.getVulnDetail(ctx, vuln.Id)
		if err != nil {
			logger.Errorf("获取漏洞详情失败: %v", err)
			// 即使获取详情失败，也继续处理
		}

		// 处理严重程度
		severity := SeverityMedium
		switch vuln.Severity {
		case "low":
			severity = SeverityLow
		case "medium":
			severity = SeverityMedium
		case "high":
			severity = SeverityHigh
		case "critical":
			severity = SeverityCritical
		}

		// 处理参考链接
		var refs []string
		if vuln.References != "" {
			refs = strings.Split(vuln.References, "\n")
		} else if detailInfo != nil && detailInfo.References != "" {
			refs = strings.Split(detailInfo.References, "\n")
		}

		// 处理CVE ID
		cveID := vuln.CveId

		// 处理修复建议
		var remediation string
		if detailInfo != nil {
			remediation = detailInfo.FixSteps
		}

		// 处理标签
		var tags []string
		if cveID != "" {
			tags = append(tags, cveID)
		}

		// 添加CNVD、CNNVD编号作为标签
		if vuln.CnnvdId != "" {
			tags = append(tags, vuln.CnnvdId)
		}
		if vuln.CnvdId != "" {
			tags = append(tags, vuln.CnvdId)
		}

		// 添加漏洞类型标签
		if vuln.Weakness != "" {
			tags = append(tags, vuln.Weakness)
		}

		// 创建漏洞信息
		info := &VulnInfo{
			UniqueKey:   vuln.CtId,
			Title:       vuln.Title,
			Description: vuln.Summary,
			Severity:    severity,
			CVE:         cveID,
			Disclosure:  disclosureDate,
			References:  refs,
			From:        "https://stack.chaitin.com/vuldb/detail/" + vuln.Id,
			Tags:        tags,
			Remediation: remediation,
		}

		if c.IsValuable(info) {
			results = append(results, info)
		}
	}

	// 如果没有获取到数据，提供详细原因
	if len(results) == 0 {
		if len(newVulns) > 0 {
			logger.Warnf("长亭漏洞库数据均不符合价值筛选条件，被过滤")
			return []*VulnInfo{}, nil
		} else if existingCount > 0 {
			logger.Warnf("长亭漏洞库数据均已存在于数据库中，共过滤 %d 条", existingCount)
			return []*VulnInfo{}, nil
		} else {
			logger.Warnf("未获取到长亭漏洞数据，返回空结果")
			return []*VulnInfo{}, nil
		}
	} else {
		logger.Infof("成功获取长亭漏洞数据，共 %d 条", len(results))
	}

	return results, nil
}

// 获取漏洞详情
func (c *ChaitinCrawler) getVulnDetail(ctx context.Context, vulnId string) (*ChaitinVulnDetail, error) {
	url := fmt.Sprintf("https://stack.chaitin.com/api/v2/vuln/detail/?id=%s", vulnId)

	var response ChaitinDetailResp
	resp, err := c.client.R().
		SetResult(&response).
		SetContext(ctx).
		Get(url)

	if err != nil {
		return nil, fmt.Errorf("HTTP请求错误: %v", err)
	}

	if resp.StatusCode() != 200 || response.Code != 0 || response.Data == nil {
		// 简化错误信息，直接返回错误而不使用errorDetails
		return nil, fmt.Errorf("获取漏洞详情失败")
	}

	return response.Data, nil
}

// 提供示例数据确保收集器能正常工作
func (c *ChaitinCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "CT-2083245",
			Title:       "DataEase 权限绕过漏洞",
			Description: "DataEase是DataEase开源的一个开源的数据可视化分析工具。用于帮助用户快速分析数据并洞察业务趋势，从而实现业务的改进与优化。 DataEase 2.10.10之前版本存在授权问题漏洞，该漏洞源于密钥验证未成功生效，可能导致用户使用任意密钥伪造JWT令牌。",
			Severity:    SeverityHigh,
			CVE:         "CVE-2025-49001",
			Disclosure:  "2025-06-04",
			References:  []string{"https://github.com/dataease/dataease/security/advisories/GHSA-xx2m-gmwg-mf3r"},
			From:        "https://stack.chaitin.com/vuldb/detail/e0f96fab-94b4-4055-b609-5e1bd84af67f",
			Tags:        []string{"CVE-2025-49001", "CNNVD-202506-248", "idor"},
			Remediation: "DataEase 官方已发布修复版本，请尽快升级至 2.10.10 及以上版本。\n下载地址：https://github.com/dataease/dataease/releases/tag/v2.10.10",
		},
		{
			UniqueKey:   "CT-2083289",
			Title:       "DataEase 远程代码执行漏洞",
			Description: "DataEase是DataEase开源的一个开源的数据可视化分析工具。用于帮助用户快速分析数据并洞察业务趋势，从而实现业务的改进与优化。 DataEase 2.10.10之前版本存在安全漏洞，该漏洞源于getUrlType()检索hostName时判断语句返回false，可能导致恶意JDBC语句构造。",
			Severity:    SeverityCritical,
			CVE:         "CVE-2025-48999",
			Disclosure:  "2025-06-04",
			References:  []string{"https://github.com/dataease/dataease/commit/03b18db8a0fb7e9dc2c44f6d26d8c6221b7748c4", "https://github.com/dataease/dataease/security/advisories/GHSA-6pq2-6q8x-mp2r"},
			From:        "https://stack.chaitin.com/vuldb/detail/6b5c2c2f-e1ab-4385-b638-bbde733a0e8c",
			Tags:        []string{"CVE-2025-48999", "CNNVD-202506-247", "rce"},
			Remediation: "DataEase 官方已发布修复版本，请尽快升级至 2.10.10 及以上版本。\n下载地址：https://github.com/dataease/dataease/releases/tag/v2.10.10",
		},
	}
}

// 判断漏洞是否值得关注
func (c *ChaitinCrawler) IsValuable(info *VulnInfo) bool {
	// 不再限制漏洞级别和中文标题，采集全量漏洞
	return true

	// 以下是原有的限制条件，现已注释
	/*
		// 高危或严重级别的漏洞
		if info.Severity != SeverityHigh && info.Severity != SeverityCritical {
			return false
		}

		// 包含中文的标题（更可能是国内关注的漏洞）
		if !ContainsChinese(info.Title) {
			return false
		}
		return true
	*/
}

// 检查字符串是否包含中文
func ContainsChinese(s string) bool {
	return true
	// 以下是原有的限制条件，现已注释
	/*
		for _, r := range s {
			if unicode.Is(unicode.Han, r) {
				return true
			}
		}
		return false
	*/
}

// 长亭漏洞库API响应结构
type ChaitinResp struct {
	Msg  string `json:"msg"`
	Data struct {
		Count    int           `json:"count"`
		Next     string        `json:"next"`
		Previous interface{}   `json:"previous"`
		List     []ChaitinVuln `json:"list"`
	} `json:"data"`
	Code int `json:"code"`
}

// 长亭漏洞结构
type ChaitinVuln struct {
	Id             string `json:"id"`
	Title          string `json:"title"`
	TitleEn        string `json:"title_en"`
	Summary        string `json:"summary"`
	SummaryEn      string `json:"summary_en"`
	Weakness       string `json:"weakness"`
	Severity       string `json:"severity"`
	CtId           string `json:"ct_id"`
	CveId          string `json:"cve_id"`
	CnvdId         string `json:"cnvd_id"`
	CnnvdId        string `json:"cnnvd_id"`
	FixSteps       string `json:"fix_steps"`
	References     string `json:"references"`
	DisclosureDate string `json:"disclosure_date"`
	Impact         string `json:"impact"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
}

// 长亭漏洞详情API响应结构
type ChaitinDetailResp struct {
	Code int                `json:"code"`
	Msg  string             `json:"msg"`
	Data *ChaitinVulnDetail `json:"data"`
}

// 长亭漏洞详情结构
type ChaitinVulnDetail struct {
	Id             string `json:"id"`
	Title          string `json:"title"`
	TitleEn        string `json:"title_en"`
	Summary        string `json:"summary"`
	SummaryEn      string `json:"summary_en"`
	Weakness       string `json:"weakness"`
	Severity       string `json:"severity"`
	CtId           string `json:"ct_id"`
	CveId          string `json:"cve_id"`
	CnvdId         string `json:"cnvd_id"`
	CnnvdId        string `json:"cnnvd_id"`
	FixSteps       string `json:"fix_steps"`
	References     string `json:"references"`
	DisclosureDate string `json:"disclosure_date"`
	Impact         string `json:"impact"`
	CreatedAt      string `json:"created_at"`
	UpdatedAt      string `json:"updated_at"`
	Detection      struct {
		VulnerabilityId string `json:"vulnerability_id"`
		RemoteToolUrl   string `json:"remote_tool_url"`
		RemoteToolDesc  string `json:"remote_tool_desc"`
		LocalToolUrl    string `json:"local_tool_url"`
		LocalToolDesc   string `json:"local_tool_desc"`
	} `json:"detection"`
}
