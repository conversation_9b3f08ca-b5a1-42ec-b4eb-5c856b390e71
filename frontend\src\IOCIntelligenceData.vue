<template>
  <div class="ioc-source-data-management">
    <div class="header">
      <h2>IOC源数据管理</h2>
      <div class="actions">
        <el-button type="warning" @click="clearUUIDRecords">
          <el-icon><Delete /></el-icon>
          清除UUID记录
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedRows.length === 0"
          @click="batchDeleteData"
        >
          <el-icon><Delete /></el-icon>
          批量删除 ({{ selectedRows.length }})
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="攻击IP">
          <el-input v-model="searchForm.attack_ip" placeholder="请输入攻击IP" clearable />
        </el-form-item>
        <el-form-item label="受害IP">
          <el-input v-model="searchForm.victim_ip" placeholder="请输入受害IP（支持模糊搜索）" clearable />
        </el-form-item>
        <el-form-item label="来源标签">
          <el-select v-model="searchForm.source_label" placeholder="请选择来源标签" clearable>
            <el-option label="通信中心" value="通信中心" />
            <el-option label="交通运输部" value="交通运输部" />
          </el-select>
        </el-form-item>
        <el-form-item label="攻击类别">
          <el-input v-model="searchForm.category" placeholder="请输入攻击类别（支持模糊搜索）" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchData">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" v-loading="loading" stripe border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="80" :index="getTableIndex" />
      <el-table-column prop="attackIp" label="攻击IP" width="150" />
      <el-table-column prop="victimIp" label="受害IP" width="200" show-overflow-tooltip>
        <template #default="scope">
          <div class="victim-ip-container">
            <div class="primary-victim-ip">{{ scope.row.victimIp }}</div>
            <div v-if="scope.row.victimIps && getVictimIPsList(scope.row.victimIps).length > 1" class="additional-victims">
              <el-tag size="small" type="info" effect="plain">
                +{{ getVictimIPsList(scope.row.victimIps).length - 1 }}个目标
              </el-tag>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  <div class="victim-ips-tooltip">
                    <div class="tooltip-title">所有受害IP:</div>
                    <div v-for="ip in getVictimIPsList(scope.row.victimIps)" :key="ip" class="tooltip-ip">
                      {{ ip }}
                    </div>
                  </div>
                </template>
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sourceLabel" label="来源标签" width="120" show-overflow-tooltip>
        <template #default="scope">
          <el-tag
            v-if="scope.row.sourceLabel"
            size="small"
            :type="getSourceLabelType(scope.row.sourceLabel)"
            class="source-label-tag"
          >
            {{ scope.row.sourceLabel }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="攻击类别" width="150" show-overflow-tooltip>
        <template #default="scope">
          <el-tag
            v-if="scope.row.category"
            size="small"
            type="warning"
            effect="plain"
            class="category-tag"
          >
            {{ scope.row.category }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="attackCount" label="攻击次数" width="100" />

      <el-table-column prop="threatScore" label="威胁评分" width="100">
        <template #default="scope">
          <el-tag
            :type="getThreatScoreType(scope.row.threatScore)"
            effect="dark"
            size="small"
          >
            {{ scope.row.threatScore ? scope.row.threatScore.toFixed(2) : '0.00' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="firstAttackTime" label="首次攻击时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.firstAttackTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastAttackTime" label="最后攻击时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.lastAttackTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button 
            type="danger" 
            size="small" 
            @click="deleteData(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.page_size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Delete, InfoFilled } from '@element-plus/icons-vue'
import api from './api'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  attack_ip: '',
  victim_ip: '',
  source_label: '',
  category: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})



// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...searchForm
    }
    
    const response = await api.getIOCIntelligenceData(params)
    tableData.value = response.data.data.list
    pagination.total = response.data.data.pagination.total
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索数据
const searchData = () => {
  pagination.page = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    attack_ip: '',
    victim_ip: '',
    source_label: '',
    category: ''
  })
  pagination.page = 1
  fetchData()
}

// 获取来源标签的标签类型
const getSourceLabelType = (label) => {
  switch (label) {
    case '通信中心':
      return 'primary'
    case '交通运输部':
      return 'success'
    default:
      return 'info'
  }
}

// 刷新数据
const refreshData = () => {
  fetchData()
}

// 计算表格序号
const getTableIndex = (index: number) => {
  return (pagination.page - 1) * pagination.page_size + index + 1
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.page_size = val
  pagination.page = 1
  fetchData()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchData()
}

// 清除UUID记录
const clearUUIDRecords = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有UUID去重记录吗？<br/><br/>' +
      '<strong style="color: #e6a23c;">⚠️ 警告：</strong><br/>' +
      '• 清除后，之前处理过的数据可能会被重新采集<br/>' +
      '• 这可能导致数据重复，请谨慎操作<br/>' +
      '• 此操作不可撤销',
      '清除UUID记录确认',
      {
        confirmButtonText: '确定清除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'uuid-clear-confirm'
      }
    )

    await api.clearUUIDRecords()
    ElMessage.success('UUID记录清除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除UUID记录失败:', error)
      ElMessage.error('清除UUID记录失败')
    }
  }
}

// 删除数据
const deleteData = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await api.delete(`/admin/ioc-intelligence-data/${row.id}`)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    }
  }
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量删除数据
const batchDeleteData = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 提取ID数组
    const ids = selectedRows.value.map(row => row.id)

    // 调用批量删除API
    await api.batchDeleteIOCIntelligenceData(ids)

    ElMessage.success(`成功删除 ${selectedRows.value.length} 条记录`)
    selectedRows.value = []
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除失败:', error)
    }
  }
}



// 获取威胁评分类型
const getThreatScoreType = (score: number) => {
  if (score >= 8) return 'danger'
  if (score >= 6.5) return 'warning'
  if (score >= 5) return 'primary'
  if (score >= 3) return 'info'
  return 'success'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'

  // 尝试解析时间戳字符串
  const timestamp = parseInt(dateTime)
  if (isNaN(timestamp)) return 'Invalid Date'

  // 判断是毫秒还是秒级时间戳
  const date = timestamp > 9999999999 ? new Date(timestamp) : new Date(timestamp * 1000)

  if (isNaN(date.getTime())) return 'Invalid Date'

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

// 获取受害IP列表
const getVictimIPsList = (victimIpsJson: string) => {
  if (!victimIpsJson) return []
  try {
    const parsed = JSON.parse(victimIpsJson)
    return Array.isArray(parsed) ? parsed : []
  } catch (error) {
    console.error('解析受害IP列表失败:', error)
    return []
  }
}



// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 多标签容器样式 */
.victim-ips-container,
.source-labels-container,
.categories-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 标签样式 */
.victim-ip-tag,
.source-label-tag,
.category-tag {
  margin: 2px 0;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 受害IP标签特殊样式 */
.victim-ip-tag {
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

/* 来源标签特殊样式 */
.source-label-tag {
  font-weight: 500;
}

/* 攻击类别标签特殊样式 */
.category-tag {
  font-size: 11px;
}

/* 表格行高度调整 */
:deep(.el-table .el-table__row) {
  height: auto;
  min-height: 50px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

/* 灰色文本样式 */
.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}

/* 页面布局样式 */
.ioc-intelligence-data {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 受害IP容器样式 */
.victim-ip-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.primary-victim-ip {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #303133;
}

.additional-victims {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  cursor: pointer;
  color: #909399;
  font-size: 14px;
}

.info-icon:hover {
  color: #409eff;
}

/* 工具提示样式 */
.victim-ips-tooltip {
  max-width: 300px;
}

.tooltip-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #fff;
}

.tooltip-ip {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  padding: 2px 0;
  color: #e6e6e6;
}

/* UUID清除确认对话框样式 */
:deep(.uuid-clear-confirm) {
  .el-message-box__content {
    line-height: 1.6;
  }

  .el-message-box__message {
    font-size: 14px;
  }
}
</style>
