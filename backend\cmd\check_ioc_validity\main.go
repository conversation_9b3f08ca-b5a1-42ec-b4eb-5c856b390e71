package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查IOC情报表的有效性字段
	fmt.Println("\n📊 检查IOC情报表的有效性字段:")
	
	type IOCValidityInfo struct {
		ID            uint   `json:"id"`
		IOC           string `json:"ioc"`
		IsValid       bool   `json:"is_valid"`
		ValidityDays  int    `json:"validity_days"`
		ExpiresAt     int64  `json:"expires_at"`
		LastUpdatedAt int64  `json:"last_updated_at"`
	}
	
	var iocInfos []IOCValidityInfo
	err = db.Table("ioc_intelligence").
		Select("id, ioc, is_valid, validity_days, expires_at, last_updated_at").
		Limit(10).
		Find(&iocInfos).Error
	
	if err != nil {
		log.Fatalf("查询IOC情报失败: %v", err)
	}
	
	fmt.Printf("📋 IOC情报有效性信息（前10条）:\n")
	for _, info := range iocInfos {
		status := "有效"
		if !info.IsValid {
			status = "无效"
		} else if info.ExpiresAt > 0 && info.ExpiresAt < 1732464000 { // 大概的当前时间戳
			status = "已过期"
		}
		
		fmt.Printf("  ID:%d, IOC:%s, IsValid:%t, ValidityDays:%d, ExpiresAt:%d, Status:%s\n",
			info.ID, info.IOC, info.IsValid, info.ValidityDays, info.ExpiresAt, status)
	}
	
	// 检查有效性字段的分布
	fmt.Println("\n📊 有效性字段分布:")
	
	type ValidityCount struct {
		IsValid bool  `json:"is_valid"`
		Count   int64 `json:"count"`
	}
	
	var validityCounts []ValidityCount
	err = db.Table("ioc_intelligence").
		Select("is_valid, COUNT(*) as count").
		Group("is_valid").
		Find(&validityCounts).Error
	
	if err != nil {
		log.Fatalf("查询有效性分布失败: %v", err)
	}
	
	for _, vc := range validityCounts {
		status := "有效"
		if !vc.IsValid {
			status = "无效"
		}
		fmt.Printf("  %s: %d 条\n", status, vc.Count)
	}
	
	// 检查过期情况
	fmt.Println("\n📊 过期情况检查:")
	
	var expiredCount int64
	currentTime := int64(1732464000) // 大概的当前时间戳
	db.Table("ioc_intelligence").
		Where("expires_at > 0 AND expires_at < ?", currentTime).
		Count(&expiredCount)
	fmt.Printf("  已过期但未标记为无效: %d 条\n", expiredCount)
	
	var neverExpireCount int64
	db.Table("ioc_intelligence").
		Where("expires_at = 0").
		Count(&neverExpireCount)
	fmt.Printf("  永不过期: %d 条\n", neverExpireCount)
}
