package main

import (
	"context"
	"fmt"
	"log"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/database"
	"vulnerability_push/internal/service"
	"vulnerability_push/internal/utils"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	level := utils.ParseLogLevel(cfg.Log.Level)
	loggerInstance := utils.NewConsoleLogger(level, true)

	// 创建数据库管理器
	dbConfig := &database.DatabaseConfig{
		Type: cfg.Database.Type,
		MySQL: database.MySQLConfig{
			Host:     cfg.Database.MySQL.Host,
			Port:     cfg.Database.MySQL.Port,
			User:     cfg.Database.MySQL.User,
			Password: cfg.Database.MySQL.Password,
			Database: cfg.Database.MySQL.Database,
			Charset:  cfg.Database.MySQL.Charset,
			Loc:      cfg.Database.MySQL.Loc,
		},
	}
	dbManager := database.NewManager(dbConfig, loggerInstance)

	// 连接数据库
	if err := dbManager.DatabaseManager.Connect(); err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer dbManager.Close()

	// 创建用户服务
	db := dbManager.GetDB()
	userService := service.NewUserService(db)

	fmt.Println("开始测试用户数据...")

	// 检查用户表中的数据
	var userCount int64
	db.Table("users").Count(&userCount)
	fmt.Printf("用户表中的用户数量: %d\n", userCount)

	// 获取所有用户
	ctx := context.Background()
	req := &service.GetUsersRequest{
		Page:     1,
		PageSize: 10,
	}
	
	users, err := userService.GetUsers(ctx, req)
	if err != nil {
		log.Fatalf("获取用户列表失败: %v", err)
	}

	fmt.Printf("通过服务获取的用户数量: %d\n", len(users.Users))
	
	for i, user := range users.Users {
		fmt.Printf("用户 %d: ID=%d, Username=%s, Email=%s, Role=%s, APIKey=%s\n", 
			i+1, user.ID, user.Username, user.Email, user.Role, user.APIKey)
	}

	// 测试获取特定用户
	if len(users.Users) > 0 {
		userID := users.Users[0].ID
		fmt.Printf("\n测试获取用户ID %d:\n", userID)
		
		user, err := userService.GetUserByID(ctx, userID)
		if err != nil {
			fmt.Printf("获取用户失败: %v\n", err)
		} else {
			fmt.Printf("成功获取用户: ID=%d, Username=%s, Email=%s, Role=%s, APIKey=%s\n", 
				user.ID, user.Username, user.Email, user.Role, user.APIKey)
		}
	}

	fmt.Println("用户数据测试完成！")
}
