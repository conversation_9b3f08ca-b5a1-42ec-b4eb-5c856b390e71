package handlers

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/internal/service"
	"vulnerability_push/IOC_Feed"
)

// ProductionStrategyHandler 生产策略处理器
type ProductionStrategyHandler struct {
	*BaseHandler
	db *gorm.DB
}

// NewProductionStrategyHandler 创建生产策略处理器
func NewProductionStrategyHandler(db *gorm.DB) *ProductionStrategyHandler {
	return &ProductionStrategyHandler{
		BaseHandler: NewBaseHandler(),
		db:          db,
	}
}

// GetProductionStrategiesRequest 获取生产策略列表请求
type GetProductionStrategiesRequest struct {
	service.PaginationRequest
	Name   string `form:"name"`
	Status string `form:"status"`
}

// GetProductionStrategies 获取生产策略列表
func (h *ProductionStrategyHandler) GetProductionStrategies(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetProductionStrategiesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()

	// 构建查询
	query := h.db.Model(&models.ProductionStrategy{})

	// 应用过滤条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取生产策略总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var strategies []models.ProductionStrategy
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&strategies).Error; err != nil {
		h.InternalServerError(c, "获取生产策略列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, strategies, total, req.Page, req.PageSize)
}

// GetProductionStrategyConfig 获取生产策略配置
func (h *ProductionStrategyHandler) GetProductionStrategyConfig(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 查找第一个策略作为全局配置，如果不存在则创建默认配置
	var strategy models.ProductionStrategy
	result := h.db.First(&strategy)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 创建默认配置
			strategy = models.ProductionStrategy{
				Name:                  "生产策略配置",
				Description:           "IOC情报生产策略全局配置",
				Status:                "enabled",
				AttackCountThreshold:  3,
				ThreatScoreThreshold:  2,
				EnableThreatScoring:   true,
				RiskLevelFilter:       "严重,高危,中危",
				DefaultValidityDays:   30,   // 默认有效期30天
				EnableValidityControl: true, // 启用有效期控制
				ScheduleEnabled:       false,
				ScheduleInterval:      60,
				TimeRangeHours:        24,
			}

			if err := h.db.Create(&strategy).Error; err != nil {
				h.InternalServerError(c, "创建默认配置失败: "+err.Error())
				return
			}
		} else {
			h.InternalServerError(c, "获取生产策略失败: "+result.Error.Error())
			return
		}
	}

	fmt.Printf("🔍 返回生产策略配置: ID=%d, 名称=%s, 攻击次数阈值=%d, 威胁评分阈值=%d\n",
		strategy.ID, strategy.Name, strategy.AttackCountThreshold, strategy.ThreatScoreThreshold)

	h.Success(c, strategy)
}

// CreateProductionStrategyRequest 创建生产策略请求
type CreateProductionStrategyRequest struct {
	Name                  string `json:"name" binding:"required"`
	Description           string `json:"description"`
	AttackCountThreshold  int    `json:"attackCountThreshold"`
	ThreatScoreThreshold  int    `json:"threatScoreThreshold"`
	EnableThreatScoring   bool   `json:"enableThreatScoring"`
	RiskLevelFilter       string `json:"riskLevelFilter"`

	// 有效期配置
	DefaultValidityDays   int    `json:"defaultValidityDays"`   // 默认有效期天数（1-100天）
	EnableValidityControl bool   `json:"enableValidityControl"` // 是否启用有效期控制

	ScheduleEnabled       bool   `json:"scheduleEnabled"`
	ScheduleInterval      int    `json:"scheduleInterval"`
	TimeRangeHours        int    `json:"timeRangeHours"`
	AllowedCountries      string `json:"allowedCountries"`
	BlockedCountries      string `json:"blockedCountries"`
	AllowedIPRanges       string `json:"allowedIPRanges"`
	BlockedIPRanges       string `json:"blockedIPRanges"`
}

// CreateProductionStrategy 创建生产策略
func (h *ProductionStrategyHandler) CreateProductionStrategy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateProductionStrategyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查名称是否已存在
	var count int64
	h.db.Model(&models.ProductionStrategy{}).Where("name = ?", req.Name).Count(&count)
	if count > 0 {
		h.BadRequest(c, "策略名称已存在")
		return
	}

	// 设置默认值
	if req.AttackCountThreshold <= 0 {
		req.AttackCountThreshold = 1
	}
	if req.ScheduleInterval <= 0 {
		req.ScheduleInterval = 60
	}
	if req.TimeRangeHours <= 0 {
		req.TimeRangeHours = 24
	}

	// 验证有效期天数
	if req.DefaultValidityDays < 1 || req.DefaultValidityDays > 100 {
		req.DefaultValidityDays = 30 // 默认30天
	}

	strategy := models.ProductionStrategy{
		Name:                  req.Name,
		Description:           req.Description,
		Status:                "disabled", // 默认禁用
		AttackCountThreshold:  req.AttackCountThreshold,
		ThreatScoreThreshold:  req.ThreatScoreThreshold,
		EnableThreatScoring:   req.EnableThreatScoring,
		RiskLevelFilter:       req.RiskLevelFilter,
		DefaultValidityDays:   req.DefaultValidityDays,
		EnableValidityControl: req.EnableValidityControl,
		ScheduleEnabled:       req.ScheduleEnabled,
		ScheduleInterval:      req.ScheduleInterval,
		TimeRangeHours:        req.TimeRangeHours,
		AllowedCountries:      req.AllowedCountries,
		BlockedCountries:      req.BlockedCountries,
		AllowedIPRanges:       req.AllowedIPRanges,
		BlockedIPRanges:       req.BlockedIPRanges,
	}

	if err := h.db.Create(&strategy).Error; err != nil {
		h.InternalServerError(c, "创建生产策略失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "创建生产策略成功", strategy)
}

// UpdateProductionStrategyRequest 更新生产策略请求
type UpdateProductionStrategyRequest struct {
	Name                  string `json:"name"`
	Description           string `json:"description"`
	AttackCountThreshold  int    `json:"attackCountThreshold"`
	ThreatScoreThreshold  int    `json:"threatScoreThreshold"`
	EnableThreatScoring   bool   `json:"enableThreatScoring"`
	RiskLevelFilter       string `json:"riskLevelFilter"`

	// 有效期配置
	DefaultValidityDays   int    `json:"defaultValidityDays"`   // 默认有效期天数（1-100天）
	EnableValidityControl bool   `json:"enableValidityControl"` // 是否启用有效期控制

	ScheduleEnabled       bool   `json:"scheduleEnabled"`
	ScheduleInterval      int    `json:"scheduleInterval"`
	TimeRangeHours        int    `json:"timeRangeHours"`
	AllowedCountries      string `json:"allowedCountries"`
	BlockedCountries      string `json:"blockedCountries"`
	AllowedIPRanges       string `json:"allowedIPRanges"`
	BlockedIPRanges       string `json:"blockedIPRanges"`
}

// UpdateProductionStrategy 更新生产策略
func (h *ProductionStrategyHandler) UpdateProductionStrategy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id := c.Param("id")
	if id == "" {
		h.BadRequest(c, "无效的策略ID")
		return
	}

	var strategy models.ProductionStrategy
	if err := h.db.First(&strategy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "生产策略不存在")
			return
		}
		h.InternalServerError(c, "查询生产策略失败: "+err.Error())
		return
	}

	var req UpdateProductionStrategyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查名称是否与其他记录冲突
	if req.Name != "" && req.Name != strategy.Name {
		var count int64
		h.db.Model(&models.ProductionStrategy{}).Where("name = ? AND id != ?", req.Name, strategy.ID).Count(&count)
		if count > 0 {
			h.BadRequest(c, "策略名称已存在")
			return
		}
		strategy.Name = req.Name
	}

	// 更新字段
	if req.Description != "" {
		strategy.Description = req.Description
	}
	if req.AttackCountThreshold > 0 {
		strategy.AttackCountThreshold = req.AttackCountThreshold
	}
	if req.ThreatScoreThreshold >= 0 {
		strategy.ThreatScoreThreshold = req.ThreatScoreThreshold
	}
	strategy.EnableThreatScoring = req.EnableThreatScoring
	if req.RiskLevelFilter != "" {
		strategy.RiskLevelFilter = req.RiskLevelFilter
	}

	// 有效期配置更新
	if req.DefaultValidityDays >= 1 && req.DefaultValidityDays <= 100 {
		strategy.DefaultValidityDays = req.DefaultValidityDays
	}
	strategy.EnableValidityControl = req.EnableValidityControl

	strategy.ScheduleEnabled = req.ScheduleEnabled
	if req.ScheduleInterval > 0 {
		strategy.ScheduleInterval = req.ScheduleInterval
	}
	if req.TimeRangeHours > 0 {
		strategy.TimeRangeHours = req.TimeRangeHours
	}
	strategy.AllowedCountries = req.AllowedCountries
	strategy.BlockedCountries = req.BlockedCountries
	strategy.AllowedIPRanges = req.AllowedIPRanges
	strategy.BlockedIPRanges = req.BlockedIPRanges

	if err := h.db.Save(&strategy).Error; err != nil {
		h.InternalServerError(c, "更新生产策略失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "更新生产策略成功", nil)
}

// DeleteProductionStrategy 删除生产策略
func (h *ProductionStrategyHandler) DeleteProductionStrategy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id := c.Param("id")
	if id == "" {
		h.BadRequest(c, "无效的策略ID")
		return
	}

	var strategy models.ProductionStrategy
	if err := h.db.First(&strategy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "生产策略不存在")
			return
		}
		h.InternalServerError(c, "查询生产策略失败: "+err.Error())
		return
	}

	if err := h.db.Delete(&strategy).Error; err != nil {
		h.InternalServerError(c, "删除生产策略失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "删除生产策略成功", nil)
}

// ToggleProductionStrategyStatus 切换生产策略状态
func (h *ProductionStrategyHandler) ToggleProductionStrategyStatus(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id := c.Param("id")
	if id == "" {
		h.BadRequest(c, "无效的策略ID")
		return
	}

	var strategy models.ProductionStrategy
	if err := h.db.First(&strategy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "生产策略不存在")
			return
		}
		h.InternalServerError(c, "查询生产策略失败: "+err.Error())
		return
	}

	// 切换状态
	if strategy.Status == "enabled" {
		strategy.Status = "disabled"
	} else {
		strategy.Status = "enabled"
	}

	if err := h.db.Save(&strategy).Error; err != nil {
		h.InternalServerError(c, "更新策略状态失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, fmt.Sprintf("策略已%s",
		map[string]string{"enabled": "启用", "disabled": "禁用"}[strategy.Status]), nil)
}

// ExecuteProductionStrategy 执行生产策略
func (h *ProductionStrategyHandler) ExecuteProductionStrategy(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id := c.Param("id")
	if id == "" {
		h.BadRequest(c, "无效的策略ID")
		return
	}

	var strategy models.ProductionStrategy
	if err := h.db.First(&strategy, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "生产策略不存在")
			return
		}
		h.InternalServerError(c, "查询生产策略失败: "+err.Error())
		return
	}

	fmt.Printf("🚀 收到执行生产策略请求: ID=%d, 名称=%s, 状态=%s\n", strategy.ID, strategy.Name, strategy.Status)

	if strategy.Status != "enabled" {
		fmt.Printf("❌ 策略未启用，无法执行: %s\n", strategy.Status)
		h.BadRequest(c, "策略未启用，无法执行")
		return
	}

	fmt.Printf("✅ 策略检查通过，开始异步执行...\n")

	// 异步执行策略
	go func() {
		h.ExecuteProductionStrategyAsync(&strategy)
	}()

	h.SuccessWithMessage(c, "策略执行已启动", nil)
}

// ExecuteProductionStrategyAsync 异步执行生产策略（公开方法供调度器使用）
func (h *ProductionStrategyHandler) ExecuteProductionStrategyAsync(strategy *models.ProductionStrategy) {
	fmt.Printf("🔄 开始执行生产策略: %s (ID: %d)\n", strategy.Name, strategy.ID)

	// 创建执行日志
	log := &models.ProductionStrategyLog{
		StrategyID:  strategy.ID,
		Status:      "running",
		Message:     "开始执行生产策略",
		ProcessedAt: time.Now().Unix(),
	}

	if err := h.db.Create(log).Error; err != nil {
		fmt.Printf("❌ 创建执行日志失败: %v\n", err)
		return
	}

	// 执行策略逻辑
	processedCount, generatedCount, err := h.processIOCIntelligenceData(strategy)
	if err != nil {
		// 更新失败日志
		log.Status = "failed"
		log.Message = fmt.Sprintf("执行失败: %v", err)
		log.ProcessedAt = time.Now().Unix()
		h.db.Save(log)

		fmt.Printf("❌ 生产策略执行失败: %v\n", err)
		return
	}

	// 更新成功日志
	log.Status = "success"
	log.Message = fmt.Sprintf("执行完成，处理 %d 条记录，生成 %d 条IOC情报", processedCount, generatedCount)
	log.ProcessedAt = time.Now().Unix()
	h.db.Save(log)

	fmt.Printf("✅ 生产策略执行完成: 处理 %d 条记录，生成 %d 条IOC情报\n", processedCount, generatedCount)
}

// processIOCIntelligenceData 处理IOC情报数据
func (h *ProductionStrategyHandler) processIOCIntelligenceData(strategy *models.ProductionStrategy) (int, int, error) {
	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(strategy.TimeRangeHours) * time.Hour)

	fmt.Printf("🔍 查询时间范围: %s 到 %s\n",
		startTime.Format("2006-01-02 15:04:05"),
		endTime.Format("2006-01-02 15:04:05"))

	// 先检查数据库中总共有多少条IOC情报源数据
	var totalCount int64
	h.db.Model(&models.IOCIntelligenceData{}).Count(&totalCount)
	fmt.Printf("📊 数据库中总共有 %d 条IOC情报源数据\n", totalCount)

	// 检查时间范围内的数据
	var timeRangeCount int64
	h.db.Model(&models.IOCIntelligenceData{}).
		Where("created_at BETWEEN ? AND ?", startTime.Unix(), endTime.Unix()).
		Count(&timeRangeCount)
	fmt.Printf("📊 时间范围内有 %d 条数据\n", timeRangeCount)

	// 获取符合条件的IOC情报数据 - 只处理未处理的数据
	var sourceData []models.IOCIntelligenceData
	query := h.db.Model(&models.IOCIntelligenceData{}).
		Where("created_at BETWEEN ? AND ?",
			startTime.Unix(),
			endTime.Unix())

	// 检查是否存在processed_status字段，如果存在则只处理未处理的数据
	if h.hasProcessedStatusColumn() {
		query = query.Where("processed_status = ?", "unprocessed")
		fmt.Printf("📋 使用处理状态过滤，只处理未处理的数据\n")
	} else {
		fmt.Printf("⚠️ processed_status字段不存在，处理所有数据（可能导致重复处理）\n")
	}

	// 应用攻击次数过滤
	if strategy.AttackCountThreshold > 0 {
		query = query.Where("attack_count >= ?", strategy.AttackCountThreshold)
	}

	// 应用威胁评分过滤
	if strategy.ThreatScoreThreshold > 0 {
		query = query.Where("threat_score >= ?", strategy.ThreatScoreThreshold)
	}

	// 注意：数据质量过滤已移除，改为定时任务配置

	if err := query.Find(&sourceData).Error; err != nil {
		return 0, 0, fmt.Errorf("查询IOC情报数据失败: %v", err)
	}

	fmt.Printf("✅ 从数据库获取到 %d 条源数据记录\n", len(sourceData))
	if len(sourceData) == 0 {
		fmt.Printf("⚠️  没有找到符合条件的源数据，执行结束\n")
		return 0, 0, nil
	}

	// 按攻击IP分组聚合数据，同时记录源数据ID
	type AttackIPGroup struct {
		*models.IOCIntelligenceData
		SourceDataIDs []uint // 记录该组包含的源数据ID
	}
	attackIPGroups := make(map[string]*AttackIPGroup)

	for _, data := range sourceData {
		attackIP := data.AttackIP
		if existingGroup, exists := attackIPGroups[attackIP]; exists {
			// 合并到现有分组
			existingGroup.AttackCount += data.AttackCount
			existingGroup.SourceDataIDs = append(existingGroup.SourceDataIDs, data.ID)

			// 更新威胁评分（取最高值）
			if data.ThreatScore > existingGroup.ThreatScore {
				existingGroup.ThreatScore = data.ThreatScore
			}

			// 更新时间范围
			if data.FirstAttackTime < existingGroup.FirstAttackTime {
				existingGroup.FirstAttackTime = data.FirstAttackTime
			}
			if data.LastAttackTime > existingGroup.LastAttackTime {
				existingGroup.LastAttackTime = data.LastAttackTime
			}
		} else {
			// 创建新的分组
			groupData := &AttackIPGroup{
				IOCIntelligenceData: &models.IOCIntelligenceData{
					AttackIP:        attackIP,
					VictimIP:        data.VictimIP,
					SourceLabel:     data.SourceLabel,
					Category:        data.Category,
					AttackCount:     data.AttackCount,
					FirstAttackTime: data.FirstAttackTime,
					LastAttackTime:  data.LastAttackTime,
					ThreatScore:     data.ThreatScore,
				},
				SourceDataIDs: []uint{data.ID},
			}
			attackIPGroups[attackIP] = groupData
		}
	}

	fmt.Printf("📊 合并后的攻击IP数量: %d\n", len(attackIPGroups))

	// 生成IOC情报
	fmt.Printf("\n🔄 开始生成IOC情报...\n")
	processedCount := len(sourceData)
	generatedCount := 0

	// 第一步：收集需要创建的IOC和需要更新的IOC
	var newIOCs []models.IOCIntelligence
	var updateIOCs []models.IOCIntelligence
	var newIOCValues []string // 用于批量查询天际友盟
	newIOCMap := make(map[string]int) // IOC值到newIOCs索引的映射
	var processedSourceDataIDs []uint // 记录已处理的源数据ID

	for attackIP, groupData := range attackIPGroups {
		fmt.Printf("\n处理攻击IP: %s (攻击次数: %d)\n", attackIP, groupData.AttackCount)

		// 判断是否应该生成IOC情报
		// 1. 攻击IP是外网地址 - 直接威胁
		// 2. 攻击IP是内网地址但受害IP是外网地址 - 内网感染对外攻击
		isAttackIPExternal := !IOC_Feed.IsPrivateIP(attackIP)
		hasExternalVictim := false

		// 检查是否有外网受害IP
		victimIPs := strings.Split(groupData.VictimIP, "、")
		for _, victimIP := range victimIPs {
			victimIP = strings.TrimSpace(victimIP)
			if !IOC_Feed.IsPrivateIP(victimIP) {
				hasExternalVictim = true
				break
			}
		}

		// 如果攻击IP是内网且没有外网受害者，则跳过
		if !isAttackIPExternal && !hasExternalVictim {
			fmt.Printf("  ⚠️ 跳过内网攻击内网的情况: %s\n", attackIP)
			// 即使跳过，也要标记这些源数据为已处理
			processedSourceDataIDs = append(processedSourceDataIDs, groupData.SourceDataIDs...)
			continue
		}

		// 确定IOC值和描述
		var iocValue string
		var iocDescription string

		if isAttackIPExternal {
			fmt.Printf("  🌐 外网攻击IP，生成IOC情报\n")
			iocValue = attackIP
			iocDescription = fmt.Sprintf("外网攻击IP: %s, 攻击次数: %d, 受害IP: %s, 来源: %s, 类别: %s, 威胁评分: %.2f",
				attackIP, groupData.AttackCount, groupData.VictimIP, groupData.SourceLabel, groupData.Category, groupData.ThreatScore)
		} else if hasExternalVictim {
			fmt.Printf("  🚨 内网IP攻击外网目标，疑似感染主机，生成IOC情报\n")
			// 找到第一个外网受害IP作为IOC值
			for _, victimIP := range victimIPs {
				victimIP = strings.TrimSpace(victimIP)
				if !IOC_Feed.IsPrivateIP(victimIP) {
					iocValue = victimIP
					break
				}
			}
			iocDescription = fmt.Sprintf("疑似被攻陷的外网IP: %s, 被内网IP %s 攻击 %d 次, 来源: %s, 类别: %s, 威胁评分: %.2f",
				iocValue, attackIP, groupData.AttackCount, groupData.SourceLabel, groupData.Category, groupData.ThreatScore)
		}

		// 记录该组对应的源数据ID，无论是否创建IOC都要标记为已处理
		processedSourceDataIDs = append(processedSourceDataIDs, groupData.SourceDataIDs...)

		// 检查是否已存在相同的IOC情报
		var existingIOC models.IOCIntelligence
		result := h.db.Where("ioc = ? AND ioc_type = ?", iocValue, "ip").First(&existingIOC)

		if result.Error == nil {
			// IOC已存在，准备更新
			fmt.Printf("  🔄 准备更新现有IOC情报 (ID: %d)\n", existingIOC.ID)
			fmt.Printf("  📊 当前命中次数: %d, 新增命中次数: %d\n", existingIOC.HitCount, groupData.AttackCount)

			// 累加命中次数（这是正确的逻辑，因为这是新的攻击数据）
			existingIOC.HitCount += groupData.AttackCount
			existingIOC.UpdatedAt = time.Now().Unix()

			// 更新风险等级（如果新的威胁评分更高）
			newRiskLevel := models.MapThreatScoreToRiskLevel(groupData.ThreatScore)
			if getRiskLevelScore(newRiskLevel) > getRiskLevelScore(existingIOC.RiskLevel) {
				existingIOC.RiskLevel = newRiskLevel
			}

			// 更新描述信息
			existingIOC.Description = iocDescription
			updateIOCs = append(updateIOCs, existingIOC)
		} else {
			// IOC不存在，准备创建新记录
			riskLevel := models.MapThreatScoreToRiskLevel(groupData.ThreatScore)
			location := IOC_Feed.GetIPLocation(iocValue)
			fmt.Printf("  📍 IP地理位置: %s -> %s\n", iocValue, location)

			ioc := models.IOCIntelligence{
				IOC:         iocValue,
				IOCType:     "ip",
				Location:    location,
				RiskLevel:   riskLevel,
				HitCount:    groupData.AttackCount,
				Source:      "生产策略自动生成",
				Description: iocDescription,
				Type:        groupData.Category,
				CreatedAt:   time.Now().Unix(),
				UpdatedAt:   time.Now().Unix(),
				IsValid:     true, // 新创建的IOC默认有效
			}

			// 应用生产策略的有效期设置
			strategy.ApplyValidityToIOC(&ioc)

			newIOCs = append(newIOCs, ioc)
			newIOCValues = append(newIOCValues, iocValue)
			newIOCMap[iocValue] = len(newIOCs) - 1
		}
	}

	// 第二步：批量查询天际友盟情报（仅对新IOC）
	if len(newIOCValues) > 0 {
		fmt.Printf("\n🔍 批量查询天际友盟情报，共 %d 个IOC\n", len(newIOCValues))

		service := IOC_Feed.GetGlobalTJUNService()
		batchResult := service.QueryIOCIntelligenceBatch(newIOCValues, "ip", 10)

		if batchResult != nil {
			fmt.Printf("  📊 批量查询完成，成功: %t\n", batchResult.Success)

			// 将查询结果应用到对应的IOC
			for iocValue, result := range batchResult.Results {
				if index, exists := newIOCMap[iocValue]; exists {
					newIOCs[index].TJUNQueryTime = result.QueryTime
					newIOCs[index].TJUNQueryStatus = result.GetQueryStatus()

					if result.Success {
						if serializedData, err := result.SerializeData(); err == nil {
							newIOCs[index].TJUNData = serializedData
							fmt.Printf("  ✅ %s: 天际友盟情报查询成功\n", iocValue)
						} else {
							newIOCs[index].TJUNErrorMessage = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
							fmt.Printf("  ⚠️ %s: 天际友盟数据序列化失败: %v\n", iocValue, err)
						}
					} else {
						newIOCs[index].TJUNErrorMessage = result.ErrorMessage
						fmt.Printf("  ❌ %s: 天际友盟情报查询失败: %s\n", iocValue, result.ErrorMessage)
					}
				}
			}
		} else {
			fmt.Printf("  ❌ 批量查询天际友盟服务不可用\n")
			// 为所有新IOC设置查询失败状态
			for i := range newIOCs {
				newIOCs[i].TJUNQueryStatus = "failed"
				newIOCs[i].TJUNErrorMessage = "天际友盟服务不可用"
				newIOCs[i].TJUNQueryTime = time.Now().Unix()
			}
		}
	}

	// 第2.5步：批量查询微步威胁情报（仅对新IOC）
	if len(newIOCValues) > 0 {
		fmt.Printf("\n🔍 批量查询微步威胁情报，共 %d 个IOC\n", len(newIOCValues))

		weibuService := IOC_Feed.GetGlobalWeibuService()
		if weibuService != nil {
			weibuBatchResult := weibuService.QueryIPReputationBatch(newIOCValues)

			if weibuBatchResult != nil {
				fmt.Printf("  📊 微步批量查询完成，成功: %t\n", weibuBatchResult.Success)

				// 将查询结果应用到对应的IOC
				for iocValue, result := range weibuBatchResult.Results {
					if index, exists := newIOCMap[iocValue]; exists {
						newIOCs[index].WeibuQueryTime = result.QueryTime
						newIOCs[index].WeibuQueryStatus = result.GetQueryStatus()

						if result.Success {
							if serializedData, err := result.SerializeData(); err == nil {
								newIOCs[index].WeibuData = serializedData
								fmt.Printf("  ✅ %s: 微步威胁情报查询成功\n", iocValue)
							} else {
								newIOCs[index].WeibuErrorMessage = fmt.Sprintf("序列化微步威胁情报数据失败: %v", err)
								fmt.Printf("  ⚠️ %s: 微步威胁情报数据序列化失败: %v\n", iocValue, err)
							}
						} else {
							newIOCs[index].WeibuErrorMessage = result.ErrorMessage
							fmt.Printf("  ❌ %s: 微步威胁情报查询失败: %s\n", iocValue, result.ErrorMessage)
						}
					}
				}
			} else {
				fmt.Printf("  ❌ 微步威胁情报批量查询返回空结果\n")
				// 为所有新IOC设置查询失败状态
				for i := range newIOCs {
					newIOCs[i].WeibuQueryStatus = "failed"
					newIOCs[i].WeibuErrorMessage = "微步威胁情报服务返回空结果"
					newIOCs[i].WeibuQueryTime = time.Now().Unix()
				}
			}
		} else {
			fmt.Printf("  ❌ 微步威胁情报服务不可用\n")
			// 为所有新IOC设置查询失败状态
			for i := range newIOCs {
				newIOCs[i].WeibuQueryStatus = "failed"
				newIOCs[i].WeibuErrorMessage = "微步威胁情报服务不可用"
				newIOCs[i].WeibuQueryTime = time.Now().Unix()
			}
		}
	}

	// 第三步：批量保存到数据库
	fmt.Printf("\n💾 保存IOC情报到数据库\n")

	// 保存更新的IOC
	for _, ioc := range updateIOCs {
		if err := h.db.Save(&ioc).Error; err != nil {
			fmt.Printf("  ❌ 更新IOC情报失败 (%s): %v\n", ioc.IOC, err)
		} else {
			fmt.Printf("  ✅ 更新成功: %s (命中次数: %d)\n", ioc.IOC, ioc.HitCount)
		}
	}

	// 批量创建新IOC
	if len(newIOCs) > 0 {
		if err := h.db.CreateInBatches(&newIOCs, 100).Error; err != nil {
			fmt.Printf("  ❌ 批量创建IOC情报失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 批量创建成功: %d 个IOC情报\n", len(newIOCs))
			generatedCount += len(newIOCs)
		}
	}

	generatedCount += len(updateIOCs)

	// 第四步：标记源数据为已处理
	if len(processedSourceDataIDs) > 0 && h.hasProcessedStatusColumn() {
		fmt.Printf("\n🏷️ 标记源数据为已处理\n")
		currentTime := time.Now().Unix()
		updateResult := h.db.Model(&models.IOCIntelligenceData{}).
			Where("id IN ?", processedSourceDataIDs).
			Updates(map[string]interface{}{
				"processed_status": "processed",
				"processed_at":     currentTime,
			})

		if updateResult.Error != nil {
			fmt.Printf("  ❌ 标记源数据失败: %v\n", updateResult.Error)
		} else {
			fmt.Printf("  ✅ 成功标记 %d 条源数据为已处理\n", updateResult.RowsAffected)
		}
	} else if len(processedSourceDataIDs) > 0 {
		fmt.Printf("\n⚠️ processed_status字段不存在，跳过标记源数据为已处理\n")
	}

	fmt.Printf("\n📈 执行完成: 处理 %d 条记录，生成/更新 %d 条IOC情报\n", processedCount, generatedCount)
	return processedCount, generatedCount, nil
}

// hasProcessedStatusColumn 检查是否存在processed_status字段
func (h *ProductionStrategyHandler) hasProcessedStatusColumn() bool {
	// 使用GORM的Migrator检查字段是否存在
	return h.db.Migrator().HasColumn(&models.IOCIntelligenceData{}, "processed_status")
}

// getRiskLevelScore 获取风险等级分数（用于比较）
func getRiskLevelScore(riskLevel string) int {
	switch riskLevel {
	case "严重":
		return 5
	case "高危":
		return 4
	case "中危":
		return 3
	case "低危":
		return 2
	case "信息":
		return 1
	default:
		return 0
	}
}

// GetProductionStrategyLogsRequest 获取生产策略日志请求
type GetProductionStrategyLogsRequest struct {
	service.PaginationRequest
	Status string `form:"status"`
}

// GetProductionStrategyLogs 获取生产策略执行日志
func (h *ProductionStrategyHandler) GetProductionStrategyLogs(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	strategyID := c.Param("id")
	if strategyID == "" {
		h.BadRequest(c, "无效的策略ID")
		return
	}

	var req GetProductionStrategyLogsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()

	// 构建查询
	query := h.db.Model(&models.ProductionStrategyLog{}).Where("strategy_id = ?", strategyID)

	// 添加状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取日志总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var logs []models.ProductionStrategyLog
	offset := (req.Page - 1) * req.PageSize

	if err := query.Order("processed_at DESC").Offset(offset).Limit(req.PageSize).Find(&logs).Error; err != nil {
		h.InternalServerError(c, "获取日志列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, logs, total, req.Page, req.PageSize)
}
