package IOC_Feed

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/oschwald/geoip2-golang"
)

// IPLocationInfo IP归属信息结构体
type IPLocationInfo struct {
	IP                string  `json:"ip"`                 // IP地址
	CountryCode       string  `json:"country_code"`       // 国家编码
	CountryNameEN     string  `json:"country_name_en"`    // 国家英文名称
	CountryNameCN     string  `json:"country_name_cn"`    // 国家中文名称
	SubdivisionCode   string  `json:"subdivision_code"`   // 州/省编码
	SubdivisionNameEN string  `json:"subdivision_name_en"` // 州/省英文名称
	SubdivisionNameCN string  `json:"subdivision_name_cn"` // 州/省中文名称
	CityNameEN        string  `json:"city_name_en"`       // 城市英文名称
	CityNameCN        string  `json:"city_name_cn"`       // 城市中文名称
	PostalCode        string  `json:"postal_code"`        // 邮政编码
	Longitude         float64 `json:"longitude"`          // 经度
	Latitude          float64 `json:"latitude"`           // 纬度
	IsPrivate         bool    `json:"is_private"`         // 是否为私有IP
	Error             string  `json:"error,omitempty"`    // 错误信息
}

// BaiduIPResponse 百度IP查询API响应结构体
type BaiduIPResponse struct {
	Status string `json:"status"`
	Data   []struct {
		Location string `json:"location"`
		OrigIP   string `json:"origip"`
	} `json:"data"`
}

// MeituanIPResponse 美团IP查询API响应结构体
type MeituanIPResponse struct {
	Data struct {
		IP   string `json:"ip"`
		Rgeo struct {
			Country  string `json:"country"`
			Province string `json:"province"`
			City     string `json:"city"`
			District string `json:"district"`
		} `json:"rgeo"`
	} `json:"data"`
}

// IPSearcher IP查询器
type IPSearcher struct {
	db *geoip2.Reader
}

// NewIPSearcher 创建新的IP查询器
func NewIPSearcher(dbPath string) (*IPSearcher, error) {
	// 如果没有提供完整路径，使用默认路径
	if dbPath == "" {
		dbPath = filepath.Join("GeoIP", "GeoLite2-City.mmdb")
	}

	db, err := geoip2.Open(dbPath)
	if err != nil {
		return nil, fmt.Errorf("无法打开GeoIP数据库文件 %s: %v", dbPath, err)
	}

	return &IPSearcher{db: db}, nil
}

// Close 关闭数据库连接
func (s *IPSearcher) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// IsPrivateIP 检查是否为私有IP地址
func IsPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查IPv4私有地址范围
	if ipv4 := parsedIP.To4(); ipv4 != nil {
		// 10.0.0.0/8
		if ipv4[0] == 10 {
			return true
		}
		// **********/12
		if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
			return true
		}
		// ***********/16
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return true
		}
		// *********/8 (回环地址)
		if ipv4[0] == 127 {
			return true
		}
		// ***********/16 (链路本地地址)
		if ipv4[0] == 169 && ipv4[1] == 254 {
			return true
		}
	}

	// 检查IPv6私有地址
	if parsedIP.IsLoopback() || parsedIP.IsLinkLocalUnicast() || parsedIP.IsLinkLocalMulticast() {
		return true
	}

	// 检查IPv6唯一本地地址 (fc00::/7)
	if len(parsedIP) == 16 && (parsedIP[0]&0xfe) == 0xfc {
		return true
	}

	return false
}

// SearchIP 查询IP地址归属信息
func (s *IPSearcher) SearchIP(ip string) *IPLocationInfo {
	result := &IPLocationInfo{
		IP:        ip,
		IsPrivate: IsPrivateIP(ip),
	}

	// 验证IP地址格式
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		result.Error = "无效的IP地址格式"
		return result
	}

	// 如果是私有IP，返回基本信息
	if result.IsPrivate {
		result.CountryNameCN = "内网地址"
		result.CountryNameEN = "Private Network"
		return result
	}

	// 查询GeoIP数据库
	record, err := s.db.City(parsedIP)
	if err != nil {
		result.Error = fmt.Sprintf("查询GeoIP数据库失败: %v", err)
		return result
	}

	// 填充国家信息
	result.CountryCode = record.Country.IsoCode
	result.CountryNameEN = record.Country.Names["en"]
	if cnName, exists := record.Country.Names["zh-CN"]; exists {
		result.CountryNameCN = cnName
	} else {
		result.CountryNameCN = result.CountryNameEN
	}

	// 填充州/省信息
	if len(record.Subdivisions) > 0 {
		subdivision := record.Subdivisions[0]
		result.SubdivisionCode = subdivision.IsoCode
		result.SubdivisionNameEN = subdivision.Names["en"]
		if cnName, exists := subdivision.Names["zh-CN"]; exists {
			result.SubdivisionNameCN = cnName
		} else {
			result.SubdivisionNameCN = result.SubdivisionNameEN
		}
	}

	// 填充城市信息
	result.CityNameEN = record.City.Names["en"]
	if cnName, exists := record.City.Names["zh-CN"]; exists {
		result.CityNameCN = cnName
	} else {
		result.CityNameCN = result.CityNameEN
	}

	// 填充邮政编码
	result.PostalCode = record.Postal.Code

	// 填充经纬度信息
	result.Longitude = float64(record.Location.Longitude)
	result.Latitude = float64(record.Location.Latitude)

	return result
}

// queryBaiduIP 查询百度IP归属API
func (s *IPSearcher) queryBaiduIP(ip string) string {
	url := fmt.Sprintf("https://opendata.baidu.com/api.php?co=&resource_id=6006&oe=utf8&query=%s", ip)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	var baiduResp BaiduIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&baiduResp); err != nil {
		return ""
	}

	if baiduResp.Status != "0" || len(baiduResp.Data) == 0 {
		return ""
	}

	location := baiduResp.Data[0].Location

	// 提取省市信息，去掉运营商信息
	// 例如："山西省太原市 铁通" -> "山西省太原市"
	// 直接取空格前的内容
	parts := strings.Fields(location)
	if len(parts) > 0 {
		locationPart := parts[0]

		// 检查是否为直辖市
		directMunicipalities := []string{"北京市", "上海市", "天津市", "重庆市"}
		for _, municipality := range directMunicipalities {
			if strings.Contains(locationPart, municipality) {
				return municipality
			}
		}

		return locationPart
	}

	return location
}

// queryMeituanIP 查询美团IP归属API
func (s *IPSearcher) queryMeituanIP(ip string) string {
	url := fmt.Sprintf("https://apimobile.meituan.com/locate/v2/ip/loc?rgeo=true&ip=%s", ip)

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	var meituanResp MeituanIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&meituanResp); err != nil {
		return ""
	}

	rgeo := meituanResp.Data.Rgeo

	// 如果是中国，返回省市信息
	if rgeo.Country == "中国" {
		// 检查是否为直辖市
		directMunicipalities := map[string]bool{
			"北京": true,
			"上海": true,
			"天津": true,
			"重庆": true,
		}

		// 如果是直辖市，只返回市名
		if rgeo.City != "" {
			if directMunicipalities[rgeo.City] {
				city := rgeo.City
				if !strings.HasSuffix(city, "市") {
					city += "市"
				}
				return city
			}
		}

		// 非直辖市，返回省市信息
		var parts []string

		// 添加省份信息
		if rgeo.Province != "" {
			parts = append(parts, rgeo.Province)
		}

		// 添加城市信息（如果与省份不同）
		if rgeo.City != "" && rgeo.City != rgeo.Province {
			city := rgeo.City
			// 如果城市名称不以"市"结尾，则添加"市"
			if !strings.HasSuffix(city, "市") {
				city += "市"
			}
			parts = append(parts, city)
		}

		if len(parts) > 0 {
			return strings.Join(parts, "")
		}
	}

	// 非中国或无数据，返回国家
	if rgeo.Country != "" {
		return rgeo.Country
	}

	return ""
}

// GetSimpleLocation 获取简化的IP归属信息
func (s *IPSearcher) GetSimpleLocation(ip string) string {
	info := s.SearchIP(ip)

	if info.Error != "" {
		// GeoIP查询失败，尝试第三方API
		// 优先使用百度API
		if baiduResult := s.queryBaiduIP(ip); baiduResult != "" {
			return baiduResult
		}
		// 百度API失败，尝试美团API
		if meituanResult := s.queryMeituanIP(ip); meituanResult != "" {
			return meituanResult
		}
		return "未知"
	}

	if info.IsPrivate {
		return "内网地址"
	}

	// 如果是中国或未知国家，尝试第三方API获取更详细信息
	if info.CountryCode == "CN" || info.CountryCode == "" {
		// 优先使用百度API
		if baiduResult := s.queryBaiduIP(ip); baiduResult != "" {
			return baiduResult
		}

		// 百度API失败，尝试美团API
		if meituanResult := s.queryMeituanIP(ip); meituanResult != "" {
			return meituanResult
		}

		// 第三方API失败，使用GeoIP结果
		if info.CountryCode == "CN" {
			var parts []string

			// 添加省份信息
			if info.SubdivisionNameCN != "" {
				parts = append(parts, info.SubdivisionNameCN)
			}

			// 添加城市信息
			if info.CityNameCN != "" {
				parts = append(parts, info.CityNameCN)
			}

			// 如果有省市信息，返回组合结果
			if len(parts) > 0 {
				result := ""
				for i, part := range parts {
					if i > 0 {
						result += " "
					}
					result += part
				}
				return result
			}

			return "中国"
		}
	}

	// 其他国家，直接返回国家名称
	if info.CountryNameCN != "" {
		return info.CountryNameCN
	}

	if info.CountryNameEN != "" {
		return info.CountryNameEN
	}

	return "未知"
}

// PrintSimpleLocation 打印简化的IP归属信息
func (s *IPSearcher) PrintSimpleLocation(ip string) {
	location := s.GetSimpleLocation(ip)
	fmt.Printf("%s: %s\n", ip, location)
}

// 全局IP查询器实例
var globalIPSearcher *IPSearcher

// InitGlobalIPSearcher 初始化全局IP查询器
func InitGlobalIPSearcher() error {
	searcher, err := NewIPSearcher("")
	if err != nil {
		return fmt.Errorf("初始化IP查询器失败: %v", err)
	}
	globalIPSearcher = searcher
	return nil
}

// GetGlobalIPSearcher 获取全局IP查询器实例
func GetGlobalIPSearcher() *IPSearcher {
	return globalIPSearcher
}

// CloseGlobalIPSearcher 关闭全局IP查询器
func CloseGlobalIPSearcher() error {
	if globalIPSearcher != nil {
		return globalIPSearcher.Close()
	}
	return nil
}

// GetIPLocation 获取IP地理位置信息（便捷函数）
func GetIPLocation(ip string) string {
	if globalIPSearcher == nil {
		// 如果全局查询器未初始化，尝试初始化
		if err := InitGlobalIPSearcher(); err != nil {
			log.Printf("初始化IP查询器失败: %v", err)
			return "未知"
		}
	}

	return globalIPSearcher.GetSimpleLocation(ip)
}