package handlers

// CCCC黑科技威胁情报接口实现
// 这个文件包含CCCC黑科技数据源的专用处理逻辑，包括：
// 1. API调用逻辑
// 2. 数据解析和转换
// 3. CCCC专用的IP标签映射规则
// 4. 攻击流合并和去重逻辑
//
// 其他数据源应该创建类似的独立实现文件，例如：
// - data_interface_es_alarm_impl.go (ES告警数据源)
// - data_interface_other_source_impl.go (其他数据源)

import (
	"crypto/tls"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"vulnerability_push/internal/models"
)

// SearchDataRequest CCCC黑科技API请求结构
type SearchDataRequest struct {
	Severity         string   `json:"severity"`
	Category         string   `json:"category"`
	SrcIP           string   `json:"src_ip"`
	DstIP           string   `json:"dst_ip"`
	EmailFrom       string   `json:"email_from"`
	To              string   `json:"to"`
	AttackStatus    string   `json:"attack_status"`
	AppProto        string   `json:"app_proto"`
	Method          string   `json:"method"`
	StatusCode      string   `json:"status_code"`
	MD5             string   `json:"md5"`
	XFF             string   `json:"xff"`
	Domain          string   `json:"domain"`
	Username        string   `json:"username"`
	VisitDirection  string   `json:"visit_direction"`
	IOC             string   `json:"ioc"`
	AppendQuery     string   `json:"appendQuery"`
	Start           int64    `json:"start"`
	End             int64    `json:"end"`
	QuickQuery      string   `json:"quickQuery"`
	FilterQuery     string   `json:"filterQuery"`
	IsCollectHistory bool    `json:"isCollectHistory"`
	QueryFields     []string `json:"queryFields"`
	From            int      `json:"from"`
	Size            int      `json:"size"`
	IsDeal          string   `json:"is_deal"`
	Sort            string   `json:"sort"`
	Order           string   `json:"order"`
	PageQueryMode   string   `json:"pageQueryMode"`
	Groups          []string `json:"groups"`
	TableName       string   `json:"tableName"`
}

// SearchDataRecord CCCC黑科技API返回的数据记录
type SearchDataRecord struct {
	UUID       string `json:"uuid"`
	SrcIP      string `json:"src_ip"`
	DstIP      string `json:"dst_ip"`
	SrcPort    int    `json:"src_port"`
	DstPort    int    `json:"dst_port"`
	AppProto   string `json:"app_proto"`
	Category   string `json:"category"`
	Severity   string `json:"severity"`
	IOC        string `json:"ioc"`
	Desc       string `json:"desc"`
	Timestamp  int64  `json:"timestamp"`
	SrcLabel   string `json:"src_label"`
	DstLabel   string `json:"dst_label"`
}

// SearchDataResponse CCCC黑科技API响应
type SearchDataResponse struct {
	Total           int                `json:"total"`
	Size            int                `json:"size"`
	Current         int                `json:"current"`
	Page            int                `json:"page"`
	Records         []SearchDataRecord `json:"records"`
	SearchHistoryID interface{}        `json:"searchHistoryId"`
}

// AttackSummary 攻击流汇总数据
type AttackSummary struct {
	UUID         string            `json:"uuid"`
	SrcIP        string            `json:"src_ip"`
	DstIP        string            `json:"dst_ip"`
	DstIPs       []string          `json:"dst_ips"`       // 受害IP列表（用于合并记录）
	SrcLabel     string            `json:"src_label"`
	DstLabel     string            `json:"dst_label"`
	DstLabels    []string          `json:"dst_labels"`    // 受害标签列表（用于合并记录）
	AttackCount  int               `json:"attack_count"`
	Categories   map[string]int    `json:"categories"`
	Severities   map[string]int    `json:"severities"`
	IOCs         map[string]int    `json:"iocs"`
	FirstSeen    int64             `json:"first_seen"`
	LastSeen     int64             `json:"last_seen"`
	SampleRecord SearchDataRecord  `json:"sample_record"`
	UUIDs        []string          `json:"uuids"`         // UUID列表（用于合并记录）
}

// CCCCBlackTechInterface CCCC黑科技数据接口实现
type CCCCBlackTechInterface struct {
	*BaseDataInterface
	handler *DataInterfaceHandler
}

// NewCCCCBlackTechInterface 创建CCCC黑科技接口实例
func NewCCCCBlackTechInterface(handler *DataInterfaceHandler) *CCCCBlackTechInterface {
	return &CCCCBlackTechInterface{
		BaseDataInterface: NewBaseDataInterface(handler),
		handler:          handler,
	}
}

// GetType 获取接口类型
func (c *CCCCBlackTechInterface) GetType() string {
	return "cccc_black_tech"
}

// GetDescription 获取接口描述
func (c *CCCCBlackTechInterface) GetDescription() string {
	return "CCCC黑科技威胁情报数据接口"
}

// ValidateConfig 验证配置
func (c *CCCCBlackTechInterface) ValidateConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"host", "user_key"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("缺少必需的配置项: %s", field)
		}
	}
	
	// 验证host格式
	if host, ok := config["host"].(string); !ok || host == "" {
		return fmt.Errorf("host配置无效")
	}
	
	// 验证user_key格式
	if userKey, ok := config["user_key"].(string); !ok || userKey == "" {
		return fmt.Errorf("user_key配置无效")
	}
	
	return nil
}

// GetConfigSchema 获取配置模式
func (c *CCCCBlackTechInterface) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"host": map[string]interface{}{
				"type":        "string",
				"title":       "主机地址",
				"description": "CCCC黑科技API主机地址",
				"required":    true,
				"placeholder": "例如: api.example.com",
			},
			"user_key": map[string]interface{}{
				"type":        "string",
				"title":       "用户密钥",
				"description": "CCCC黑科技API用户密钥",
				"required":    true,
				"placeholder": "请输入您的API密钥",
			},
			"target_organizations": map[string]interface{}{
				"type":        "array",
				"title":       "目标组织",
				"description": "需要监控的目标组织列表",
				"items": map[string]interface{}{
					"type": "string",
				},
				"default": []string{},
			},
		},
		"required": []string{"host", "user_key"},
	}
}

// Execute 执行数据接口采集
func (c *CCCCBlackTechInterface) Execute(dataInterface *models.DataInterface, config map[string]interface{}, log *models.DataInterfaceLog) error {
	// 验证配置
	if err := c.ValidateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}
	
	// 获取配置参数
	host := config["host"].(string)
	userKey := config["user_key"].(string)
	
	// 获取目标组织列表
	var targetOrganizations []string
	if orgs, exists := config["target_organizations"]; exists {
		if orgList, ok := orgs.([]interface{}); ok {
			for _, org := range orgList {
				if orgStr, ok := org.(string); ok {
					targetOrganizations = append(targetOrganizations, orgStr)
				}
			}
		}
	}
	
	// 计算时间范围
	timeRangeValue := dataInterface.TimeRangeValue
	if timeRangeValue <= 0 {
		timeRangeValue = 3600 // 默认1小时
	}
	
	endTime := time.Now().UnixMilli()
	startTime := endTime - int64(timeRangeValue)*1000
	
	fmt.Printf("CCCC黑科技接口采集 - 主机: %s, 时间范围: %s 到 %s\n",
		host,
		time.Unix(startTime/1000, 0).Format("2006-01-02 15:04:05"),
		time.Unix(endTime/1000, 0).Format("2006-01-02 15:04:05"))
	
	// 更新日志状态
	log.Message = "正在调用CCCC黑科技API获取数据..."
	c.handler.db.Save(log)
	
	// 调用CCCC黑科技API
	records, err := c.callCCCCBlackTechAPI(host, userKey, startTime, endTime)
	if err != nil {
		return fmt.Errorf("API调用失败: %v", err)
	}
	
	// 输出原始API数据样本
	fmt.Printf("\n=== API原始数据样本 ===\n")
	for i, record := range records {
		if i >= 5 { // 只显示前5条
			break
		}
		fmt.Printf("记录 %d: UUID=%s, SrcIP=%s, DstIP=%s, Category=%s, Severity=%s, IOC=%s, Timestamp=%s\n",
			i+1, record.UUID, record.SrcIP, record.DstIP, record.Category, record.Severity, record.IOC, record.Timestamp)
	}
	if len(records) > 5 {
		fmt.Printf("... 还有 %d 条记录\n", len(records)-5)
	}
	
	// 处理和保存数据
	processedCount, err := c.processCCCCBlackTechData(records, targetOrganizations)
	if err != nil {
		return fmt.Errorf("数据处理失败: %v", err)
	}
	
	// 更新执行日志
	log.MarkAsCompleted("success", fmt.Sprintf("成功采集并处理 %d 条数据", processedCount), processedCount)
	c.handler.db.Save(log)
	
	// 更新数据接口统计
	c.handler.updateDataInterfaceStats(dataInterface, "success", fmt.Sprintf("成功采集并处理 %d 条数据", processedCount), processedCount)
	
	fmt.Printf("CCCC黑科技接口采集完成 - 处理了 %d 条数据\n", processedCount)
	return nil
}

// callCCCCBlackTechAPI 调用CCCC黑科技API获取数据（并发优化版本）
func (c *CCCCBlackTechInterface) callCCCCBlackTechAPI(host, userKey string, startTime, endTime int64) ([]SearchDataRecord, error) {
	size := 100 // 每页获取100条记录
	maxConcurrency := c.getOptimalConcurrency() // 动态计算最大并发数

	fmt.Printf("开始调用CCCC黑科技API，每页 %d 条记录，最大并发数: %d\n", size, maxConcurrency)

	// 第一步：获取第一页数据以确定总数和总页数
	fmt.Printf("正在获取第 1 页数据（确定总数）\n")
	firstPageResult, err := c.searchAdvancedDataPage(host, userKey, startTime, endTime, 1, size)
	if err != nil {
		return nil, fmt.Errorf("获取第1页数据失败: %v", err)
	}

	allRecords := make([]SearchDataRecord, 0, firstPageResult.Total)
	allRecords = append(allRecords, firstPageResult.Records...)

	fmt.Printf("第 1 页获取到 %d 条记录，总数: %d\n", len(firstPageResult.Records), firstPageResult.Total)

	// 计算总页数
	totalPages := (firstPageResult.Total + size - 1) / size
	if totalPages <= 1 {
		fmt.Printf("API调用完成，总共获取 %d 条记录\n", len(allRecords))
		return allRecords, nil
	}

	// 第二步：并发获取剩余页面
	concurrentRecords, err := c.concurrentFetchRemainingPages(host, userKey, startTime, endTime, allRecords, 2, totalPages, size, maxConcurrency)
	if err != nil {
		fmt.Printf("并发获取失败，回退到串行模式: %v\n", err)
		return c.serialFetchRemainingPages(host, userKey, startTime, endTime, allRecords, 2, totalPages, size)
	}
	return concurrentRecords, nil
}

// concurrentFetchRemainingPages 并发获取剩余页面数据
func (c *CCCCBlackTechInterface) concurrentFetchRemainingPages(host, userKey string, startTime, endTime int64,
	initialRecords []SearchDataRecord, startPage, totalPages, size, maxConcurrency int) ([]SearchDataRecord, error) {

	if startPage > totalPages {
		return initialRecords, nil
	}

	fmt.Printf("开始并发获取第 %d-%d 页数据\n", startPage, totalPages)

	// 页面结果结构
	type PageResult struct {
		Page    int
		Records []SearchDataRecord
	}

	// 创建页面任务通道
	pageJobs := make(chan int, maxConcurrency*2)
	results := make(chan *PageResult, maxConcurrency*2)
	errors := make(chan error, maxConcurrency)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < maxConcurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for page := range pageJobs {
				fmt.Printf("Worker %d 正在获取第 %d 页数据\n", workerID, page)

				result, err := c.searchAdvancedDataPage(host, userKey, startTime, endTime, page, size)
				if err != nil {
					errors <- fmt.Errorf("Worker %d 获取第%d页数据失败: %v", workerID, page, err)
					return
				}

				results <- &PageResult{
					Page:    page,
					Records: result.Records,
				}

				fmt.Printf("Worker %d 第 %d 页获取到 %d 条记录\n", workerID, page, len(result.Records))
			}
		}(i)
	}

	// 发送页面任务
	go func() {
		defer close(pageJobs)
		for page := startPage; page <= totalPages; page++ {
			pageJobs <- page
		}
	}()

	// 收集结果
	go func() {
		wg.Wait()
		close(results)
		close(errors)
	}()

	// 处理结果和错误
	pageResults := make(map[int][]SearchDataRecord)
	expectedPages := totalPages - startPage + 1
	receivedPages := 0

	for {
		select {
		case result, ok := <-results:
			if !ok {
				results = nil
			} else {
				pageResults[result.Page] = result.Records
				receivedPages++
			}
		case err, ok := <-errors:
			if !ok {
				errors = nil
			} else {
				return nil, err
			}
		}

		if results == nil && errors == nil {
			break
		}
	}

	if receivedPages != expectedPages {
		return nil, fmt.Errorf("并发获取页面不完整: 期望%d页，实际获取%d页", expectedPages, receivedPages)
	}

	// 按页面顺序合并结果
	allRecords := make([]SearchDataRecord, 0, len(initialRecords)+expectedPages*size)
	allRecords = append(allRecords, initialRecords...)

	for page := startPage; page <= totalPages; page++ {
		if records, exists := pageResults[page]; exists {
			allRecords = append(allRecords, records...)
		}
	}

	fmt.Printf("并发API调用完成，总共获取 %d 条记录\n", len(allRecords))
	return allRecords, nil
}

// getOptimalConcurrency 根据系统资源动态计算最优并发数
func (c *CCCCBlackTechInterface) getOptimalConcurrency() int {
	// 基础并发数：根据CPU核心数计算
	cpuCount := c.dedupConfig.WorkerCount // 复用UUID去重的工作协程配置
	if cpuCount <= 0 {
		cpuCount = 4 // 默认值
	}

	// API请求的并发数通常比CPU密集型任务可以更高，因为主要是I/O等待
	// 但也不能太高，避免对目标服务器造成过大压力
	maxConcurrency := cpuCount
	if maxConcurrency > 8 {
		maxConcurrency = 8 // 限制最大并发数，避免对API服务器造成过大压力
	}
	if maxConcurrency < 2 {
		maxConcurrency = 2 // 至少保证2个并发
	}

	return maxConcurrency
}

// serialFetchRemainingPages 串行获取剩余页面数据（回退方案）
func (c *CCCCBlackTechInterface) serialFetchRemainingPages(host, userKey string, startTime, endTime int64,
	initialRecords []SearchDataRecord, startPage, totalPages, size int) ([]SearchDataRecord, error) {

	if startPage > totalPages {
		return initialRecords, nil
	}

	fmt.Printf("开始串行获取第 %d-%d 页数据\n", startPage, totalPages)

	allRecords := make([]SearchDataRecord, 0, len(initialRecords)+(totalPages-startPage+1)*size)
	allRecords = append(allRecords, initialRecords...)

	for page := startPage; page <= totalPages; page++ {
		fmt.Printf("正在获取第 %d 页数据\n", page)

		result, err := c.searchAdvancedDataPage(host, userKey, startTime, endTime, page, size)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页数据失败: %v", page, err)
		}

		allRecords = append(allRecords, result.Records...)
		fmt.Printf("第 %d 页获取到 %d 条记录，累计 %d 条\n", page, len(result.Records), len(allRecords))

		// 检查是否已获取完所有数据
		if len(result.Records) < size {
			break
		}
	}

	fmt.Printf("串行API调用完成，总共获取 %d 条记录\n", len(allRecords))
	return allRecords, nil
}

// searchAdvancedDataPage 获取单页数据
func (c *CCCCBlackTechInterface) searchAdvancedDataPage(host, userKey string, startTime, endTime int64, page, size int) (*SearchDataResponse, error) {
	// 创建HTTP客户端
	client := resty.New()
	client.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	client.SetTimeout(30 * time.Second)

	// 构建请求数据 - 使用完整的SearchDataRequest结构
	requestData := SearchDataRequest{
		Severity:         "",
		Category:         "",
		SrcIP:           "",
		DstIP:           "",
		EmailFrom:       "",
		To:              "",
		AttackStatus:    "",
		AppProto:        "",
		Method:          "",
		StatusCode:      "",
		MD5:             "",
		XFF:             "",
		Domain:          "",
		Username:        "",
		VisitDirection:  "",
		IOC:             "",
		AppendQuery:     "",
		Start:           startTime,
		End:             endTime,
		QuickQuery:      "",
		FilterQuery:     "",
		IsCollectHistory: false,
		QueryFields: []string{
			"xff", "src_ip_country", "src_ip_city", "attacker", "victim",
			"src_ip_hostName", "src_ip_isHost", "host", "server_name",
			"dst_ip_country", "dst_ip_city", "dst_ip_hostName", "dst_ip_isHost",
			"dst_port", "method", "uri", "status_code", "user_agent", "query",
			"answers", "qtype_name", "email_from", "to", "attachment_names",
			"md5", "platform", "username", "password", "timestamp", "src_ip",
			"dst_ip", "app_proto", "category", "severity", "ioc", "attack_status",
			"desc", "rid",
		},
		From:          page,
		Size:          size,
		IsDeal:        "",
		Sort:          "",
		Order:         "",
		PageQueryMode: "DATA",
		Groups:        []string{"uuid"},
		TableName:     "event_data",
	}

	// 发送POST请求
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("user-key", userKey).
		SetBody(requestData).
		SetResult(&SearchDataResponse{}).
		Post(fmt.Sprintf("https://%s/workbenchApi/furious/searchCenter/advanced/searchData", host))

	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	result, ok := resp.Result().(*SearchDataResponse)
	if !ok {
		return nil, fmt.Errorf("响应解析失败")
	}

	return result, nil
}

// processCCCCBlackTechData 处理CCCC黑科技数据
func (c *CCCCBlackTechInterface) processCCCCBlackTechData(records []SearchDataRecord, targetOrganizations []string) (int, error) {
	// 使用新的去重逻辑：先UUID去重，再攻击流合并
	attackSummaries, internalCount, whitelistCount, filteredCount := c.mergeAttackDataWithUUIDDedup(records, targetOrganizations)

	fmt.Printf("数据处理统计 - 原始记录: %d, 内网流量: %d, 白名单: %d, 非目标组织: %d, 最终合并记录: %d\n",
		len(records), internalCount, whitelistCount, filteredCount, len(attackSummaries))

	if len(attackSummaries) == 0 {
		fmt.Println("没有有效的攻击数据需要保存")
		return 0, nil
	}

	// 转换为IOC源数据格式并保存
	var sourceDataList []models.IOCIntelligenceData
	var allProcessedUUIDs []string

	for _, summary := range attackSummaries {
		// 获取主要类别
		mainCategory := c.getMainCategory(summary.Categories)

		// 确定来源标签（优先使用受害方标签）
		sourceLabel := ""
		if summary.DstLabel == "通信中心" || summary.DstLabel == "交通运输部" {
			sourceLabel = summary.DstLabel
		} else if summary.SrcLabel == "通信中心" || summary.SrcLabel == "交通运输部" {
			sourceLabel = summary.SrcLabel
		}

		sourceData := models.IOCIntelligenceData{
			UUID:            summary.UUID,
			AttackIP:        summary.SrcIP,
			VictimIP:        summary.DstIP,
			SourceLabel:     sourceLabel,
			Category:        c.GetAttackCategoryName(mainCategory),
			AttackCount:     summary.AttackCount,
			FirstAttackTime: fmt.Sprintf("%d", summary.FirstSeen),
			LastAttackTime:  fmt.Sprintf("%d", summary.LastSeen),
			ThreatScore:     c.calculateThreatScore(summary.Severities, summary.AttackCount),
		}
		sourceDataList = append(sourceDataList, sourceData)

		// 收集所有UUID用于标记为已处理
		allProcessedUUIDs = append(allProcessedUUIDs, summary.UUIDs...)
	}

	// 批量保存IOC源数据
	if err := c.BatchSaveIOCSourceData(sourceDataList); err != nil {
		return 0, fmt.Errorf("保存IOC源数据失败: %v", err)
	}

	// 批量保存所有已处理的UUID（优化版本）
	if err := c.BatchSaveProcessedUUIDs(allProcessedUUIDs, "cccc_black_tech", c.dedupConfig.SaveBatchSize); err != nil {
		fmt.Printf("批量保存UUID失败: %v\n", err)
		// 如果批量保存失败，回退到单个保存模式
		savedCount := 0
		for _, uuid := range allProcessedUUIDs {
			if uuid != "" && !c.IsUUIDProcessed(uuid, "cccc_black_tech") {
				if err := c.SaveProcessedUUID(uuid, "cccc_black_tech"); err != nil {
					fmt.Printf("保存UUID失败: %s, 错误: %v\n", uuid, err)
				} else {
					savedCount++
				}
			}
		}
		fmt.Printf("回退模式保存了 %d 个已处理的UUID\n", savedCount)
	}

	fmt.Printf("成功保存 %d 条IOC源数据\n", len(sourceDataList))
	return len(sourceDataList), nil
}

// mergeAttackDataWithUUIDDedup 使用UUID去重和攻击流合并处理数据（多线程优化版本）
func (c *CCCCBlackTechInterface) mergeAttackDataWithUUIDDedup(records []SearchDataRecord, targetOrganizations []string) (map[string]*AttackSummary, int, int, int) {
	internalCount := 0
	whitelistCount := 0
	filteredCount := 0

	// 根据数据量优化去重配置
	c.OptimizeDedupConfigForDataSize(len(records))
	fmt.Printf("数据量: %d, 使用配置 - 批次大小: %d, 工作协程: %d\n",
		len(records), c.dedupConfig.BatchSize, c.dedupConfig.WorkerCount)

	// 第一步：基础过滤，收集需要检查的UUID
	var candidateRecords []SearchDataRecord
	var uuidsToCheck []string

	for _, record := range records {
		// 检查UUID是否为空
		if record.UUID == "" {
			fmt.Printf("跳过UUID为空的记录: SrcIP=%s, DstIP=%s\n", record.SrcIP, record.DstIP)
			continue
		}

		// 统计内网流量
		if c.IsInternalTraffic(record.SrcIP, record.DstIP) {
			internalCount++
			continue
		}

		// 检查是否涉及目标组织
		if !c.ShouldIncludeAttackFlow(record.SrcIP, record.DstIP, targetOrganizations) {
			filteredCount++
			continue
		}

		// 检查白名单
		if c.IsWhitelistedIP(record.SrcIP) || c.IsWhitelistedIP(record.DstIP) {
			whitelistCount++
			continue
		}

		// 通过基础过滤的记录，加入候选列表
		candidateRecords = append(candidateRecords, record)
		uuidsToCheck = append(uuidsToCheck, record.UUID)
	}

	fmt.Printf("基础过滤完成 - 候选记录: %d 条\n", len(candidateRecords))

	// 第二步：批量UUID去重检查（多线程优化）
	var validRecords []SearchDataRecord

	if len(uuidsToCheck) > 0 {
		// 使用优化的批量检查UUID是否已处理
		processedMap, err := c.OptimizedBatchCheckUUIDsProcessed(uuidsToCheck, "cccc_black_tech")
		if err != nil {
			fmt.Printf("优化UUID去重检查失败，回退到单线程模式: %v\n", err)
			// 回退到原有的单线程模式
			for _, record := range candidateRecords {
				if !c.IsUUIDProcessed(record.UUID, "cccc_black_tech") {
					validRecords = append(validRecords, record)
				}
			}
		} else {
			// 使用批量检查结果过滤
			for _, record := range candidateRecords {
				if !processedMap[record.UUID] {
					validRecords = append(validRecords, record)
				}
			}
		}
	}

	fmt.Printf("UUID去重后有效记录: %d 条\n", len(validRecords))

	// 第三步：攻击流合并 - 按攻击流（SrcIP->DstIP）分组
	attackFlowMap := make(map[string]*AttackSummary)

	for _, record := range validRecords {
		// 生成攻击流键：攻击IP -> 受害IP（直接使用原始的SrcIP和DstIP）
		flowKey := fmt.Sprintf("%s->%s", record.SrcIP, record.DstIP)

		if existing, exists := attackFlowMap[flowKey]; exists {
			// 更新现有记录
			existing.AttackCount++
			existing.UUIDs = append(existing.UUIDs, record.UUID)

			// 更新时间范围
			if record.Timestamp < existing.FirstSeen {
				existing.FirstSeen = record.Timestamp
			}
			if record.Timestamp > existing.LastSeen {
				existing.LastSeen = record.Timestamp
			}

			// 合并目标IP
			if !c.containsString(existing.DstIPs, record.DstIP) {
				existing.DstIPs = append(existing.DstIPs, record.DstIP)
			}

			// 合并目标标签
			dstLabel := c.getCCCCSourceLabel(record.DstIP)
			if dstLabel != "" && !c.containsString(existing.DstLabels, dstLabel) {
				existing.DstLabels = append(existing.DstLabels, dstLabel)
			}

			// 统计类别
			if existing.Categories == nil {
				existing.Categories = make(map[string]int)
			}
			existing.Categories[record.Category]++

			// 统计严重程度
			if existing.Severities == nil {
				existing.Severities = make(map[string]int)
			}
			existing.Severities[record.Severity]++

			// 统计IOC
			if existing.IOCs == nil {
				existing.IOCs = make(map[string]int)
			}
			if record.IOC != "" {
				existing.IOCs[record.IOC]++
			}
		} else {
			// 创建新记录
			attackFlowMap[flowKey] = &AttackSummary{
				UUID:         record.UUID,
				SrcIP:        record.SrcIP,
				DstIP:        record.DstIP,
				DstIPs:       []string{record.DstIP},
				SrcLabel:     c.getCCCCSourceLabel(record.SrcIP),
				DstLabel:     c.getCCCCSourceLabel(record.DstIP),
				DstLabels:    []string{c.getCCCCSourceLabel(record.DstIP)},
				AttackCount:  1,
				Categories:   map[string]int{record.Category: 1},
				Severities:   map[string]int{record.Severity: 1},
				IOCs:         map[string]int{record.IOC: 1},
				FirstSeen:    record.Timestamp,
				LastSeen:     record.Timestamp,
				SampleRecord: record,
				UUIDs:        []string{record.UUID},
			}
		}
	}

	fmt.Printf("攻击流合并后记录: %d 条\n", len(attackFlowMap))

	return attackFlowMap, internalCount, whitelistCount, filteredCount
}

// getValidRecordsUUIDs 获取通过所有过滤条件的有效记录的UUID列表
func (c *CCCCBlackTechInterface) getValidRecordsUUIDs(records []SearchDataRecord, targetOrganizations []string) []string {
	var validUUIDs []string

	for _, record := range records {
		// 检查UUID是否为空
		if record.UUID == "" {
			continue
		}

		// 过滤内网流量
		if c.IsInternalTraffic(record.SrcIP, record.DstIP) {
			continue
		}

		// 检查是否涉及目标组织
		if !c.ShouldIncludeAttackFlow(record.SrcIP, record.DstIP, targetOrganizations) {
			continue
		}

		// 检查UUID是否已处理过
		if c.IsUUIDProcessed(record.UUID, "cccc_black_tech") {
			continue
		}

		// 检查白名单
		if c.IsWhitelistedIP(record.SrcIP) || c.IsWhitelistedIP(record.DstIP) {
			continue
		}

		// 通过所有过滤条件，添加到有效UUID列表
		validUUIDs = append(validUUIDs, record.UUID)
	}

	return validUUIDs
}

// containsString 检查字符串切片是否包含指定字符串
func (c *CCCCBlackTechInterface) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// getMainCategory 获取主要攻击类别
func (c *CCCCBlackTechInterface) getMainCategory(categories map[string]int) string {
	if len(categories) == 0 {
		return ""
	}

	maxCount := 0
	mainCategory := ""
	for category, count := range categories {
		if count > maxCount {
			maxCount = count
			mainCategory = category
		}
	}
	return mainCategory
}

// getMainSeverity 获取主要严重程度
func (c *CCCCBlackTechInterface) getMainSeverity(severities map[string]int) string {
	if len(severities) == 0 {
		return ""
	}

	// 按严重程度优先级排序
	priorityOrder := []string{"critical", "high", "medium", "low", "info"}

	for _, priority := range priorityOrder {
		if count, exists := severities[priority]; exists && count > 0 {
			return priority
		}
	}

	// 如果没有匹配的优先级，返回计数最多的
	maxCount := 0
	mainSeverity := ""
	for severity, count := range severities {
		if count > maxCount {
			maxCount = count
			mainSeverity = severity
		}
	}
	return mainSeverity
}

// getCCCCSourceLabel 获取CCCC黑科技接口专用的IP来源标签
// 这个方法专门为CCCC黑科技数据源设计，不同的数据源可以有不同的标签逻辑
func (c *CCCCBlackTechInterface) getCCCCSourceLabel(ip string) string {
	if ip == "" {
		return "未知"
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "未知"
	}

	if ipv4 := parsedIP.To4(); ipv4 != nil {
		// CCCC黑科技接口的IP段映射规则：
		// 10段 - 交通运输部
		if ipv4[0] == 10 {
			return "交通运输部"
		}

		// 192段 - 通信中心
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return "通信中心"
		}

		// 172段 - 内网设备
		if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
			return "内网设备"
		}
	}

	// 外网地址
	if !c.IsInternalIP(ip) {
		return "外网"
	}

	return "内网"
}

// calculateThreatScore 计算威胁评分（使用原始逻辑）
func (c *CCCCBlackTechInterface) calculateThreatScore(severities map[string]int, attackCount int) float64 {
	// 基础分数：根据最高严重程度计算
	var baseScore float64 = 0
	for severity := range severities {
		var score float64
		switch severity {
		case "高危":
			score = 8.0
		case "中危":
			score = 5.0
		case "低危":
			score = 2.0
		case "信息":
			score = 1.0
		default:
			score = 3.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	// 计算最终评分
	finalScore := baseScore * frequencyMultiplier

	// 限制评分范围在1-10之间
	if finalScore > 10.0 {
		finalScore = 10.0
	} else if finalScore < 1.0 {
		finalScore = 1.0
	}

	return finalScore
}
