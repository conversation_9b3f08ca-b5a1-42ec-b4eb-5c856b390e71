package handlers

import (
	"fmt"
)

// DataInterfaceFactory 数据接口工厂
// 负责创建和管理数据接口实例
type DataInterfaceFactory struct {
	handler *DataInterfaceHandler
}

// NewDataInterfaceFactory 创建数据接口工厂
func NewDataInterfaceFactory(handler *DataInterfaceHandler) *DataInterfaceFactory {
	return &DataInterfaceFactory{
		handler: handler,
	}
}

// CreateInterface 创建数据接口实例
func (f *DataInterfaceFactory) CreateInterface(interfaceType string) (DataInterfaceExecutor, error) {
	switch interfaceType {
	case "cccc_black_tech":
		return NewCCCCBlackTechInterface(f.handler), nil
	
	// 后续添加其他接口类型
	// case "other_interface":
	//     return NewOtherInterface(f.handler), nil
	
	default:
		return nil, fmt.Errorf("不支持的接口类型: %s", interfaceType)
	}
}

// GetSupportedTypes 获取支持的接口类型列表
func (f *DataInterfaceFactory) GetSupportedTypes() []string {
	return []string{
		"cccc_black_tech",
		// 后续添加其他接口类型
	}
}

// ValidateInterfaceType 验证接口类型是否支持
func (f *DataInterfaceFactory) ValidateInterfaceType(interfaceType string) bool {
	supportedTypes := f.GetSupportedTypes()
	for _, supportedType := range supportedTypes {
		if supportedType == interfaceType {
			return true
		}
	}
	return false
}

// GetInterfaceMetadata 获取接口元数据
func (f *DataInterfaceFactory) GetInterfaceMetadata(interfaceType string) map[string]interface{} {
	switch interfaceType {
	case "cccc_black_tech":
		return map[string]interface{}{
			"name":        "CCCC黑科技",
			"description": "CCCC黑科技威胁情报数据接口",
			"version":     "1.0.0",
			"author":      "System",
			"category":    "threat_intelligence",
			"tags":        []string{"威胁情报", "攻击检测", "IOC"},
			"dataTypes":   []string{"ip", "domain", "url", "hash"},
			"features": []string{
				"实时数据采集",
				"自动去重",
				"攻击流合并",
				"白名单过滤",
			},
		}
	
	default:
		return nil
	}
}

// GetAllInterfaceMetadata 获取所有接口的元数据
func (f *DataInterfaceFactory) GetAllInterfaceMetadata() map[string]interface{} {
	result := make(map[string]interface{})
	
	supportedTypes := f.GetSupportedTypes()
	for _, interfaceType := range supportedTypes {
		metadata := f.GetInterfaceMetadata(interfaceType)
		if metadata != nil {
			result[interfaceType] = metadata
		}
	}
	
	return result
}
