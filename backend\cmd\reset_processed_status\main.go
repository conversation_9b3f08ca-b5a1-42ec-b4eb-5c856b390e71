package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查当前处理状态分布
	fmt.Println("\n📊 重置前的处理状态分布:")
	
	type StatusCount struct {
		ProcessedStatus string `json:"processed_status"`
		Count          int64  `json:"count"`
	}
	
	var statusCounts []StatusCount
	err = db.Table("ioc_source_data").
		Select("processed_status, COUNT(*) as count").
		Group("processed_status").
		Find(&statusCounts).Error
	
	if err != nil {
		log.Fatalf("查询处理状态分布失败: %v", err)
	}
	
	for _, sc := range statusCounts {
		fmt.Printf("  %s: %d 条\n", sc.ProcessedStatus, sc.Count)
	}
	
	// 重置所有已处理的数据为未处理状态
	fmt.Println("\n🔄 重置所有已处理的数据为未处理状态...")
	
	result := db.Table("ioc_source_data").
		Where("processed_status = ?", "processed").
		Updates(map[string]interface{}{
			"processed_status": "unprocessed",
			"processed_at":     0,
		})
	
	if result.Error != nil {
		log.Fatalf("重置处理状态失败: %v", result.Error)
	}
	
	fmt.Printf("✅ 成功重置 %d 条记录的处理状态\n", result.RowsAffected)
	
	// 检查重置后的处理状态分布
	fmt.Println("\n📊 重置后的处理状态分布:")
	
	var statusCountsAfter []StatusCount
	err = db.Table("ioc_source_data").
		Select("processed_status, COUNT(*) as count").
		Group("processed_status").
		Find(&statusCountsAfter).Error
	
	if err != nil {
		log.Fatalf("查询处理状态分布失败: %v", err)
	}
	
	for _, sc := range statusCountsAfter {
		fmt.Printf("  %s: %d 条\n", sc.ProcessedStatus, sc.Count)
	}
	
	// 检查满足生产条件的数据
	fmt.Println("\n🔍 检查满足生产条件的数据:")
	
	// 检查同时满足攻击次数 >= 3 和威胁评分 >= 2 且未处理的数据
	var readyForProduction int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&readyForProduction)
	fmt.Printf("  满足生产条件且未处理: %d 条\n", readyForProduction)
	
	if readyForProduction > 0 {
		fmt.Println("\n🎉 数据重置完成！现在可以重新执行生产策略了。")
	} else {
		fmt.Println("\n⚠️  没有找到满足生产条件的数据，请检查阈值设置。")
	}
}
