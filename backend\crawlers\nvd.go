package crawlers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

// NVD漏洞采集器
type NVDCrawler struct {
	client  *resty.Client
	apiKey  string
	baseURL string
	debug   bool
}

// NVD API响应结构
type nvdResponse struct {
	Vulnerabilities []struct {
		Cve struct {
			ID                  string             `json:"id"`
			SourceIdentifier    string             `json:"sourceIdentifier"`
			Published           string             `json:"published"`
			LastModified        string             `json:"lastModified"`
			VulnStatus          string             `json:"vulnStatus"`
			Descriptions        []nvdDescription   `json:"descriptions"`
			Metrics             nvdMetrics         `json:"metrics"`
			References          []nvdReference     `json:"references"`
			Configurations      []nvdConfiguration `json:"configurations"`
			WeaknessEnumeration []struct {
				Source       string `json:"source"`
				Type         string `json:"type"`
				WeaknessList []struct {
					Description []nvdDescription `json:"description"`
					ID          string           `json:"id"`
				} `json:"weaknessList"`
			} `json:"weaknessEnumeration"`
		} `json:"cve"`
	} `json:"vulnerabilities"`
	TotalResults   int `json:"totalResults"`
	ResultsPerPage int `json:"resultsPerPage"`
	StartIndex     int `json:"startIndex"`
}

type nvdDescription struct {
	Lang  string `json:"lang"`
	Value string `json:"value"`
}

type nvdMetrics struct {
	CvssMetricV31 []struct {
		Source   string `json:"source"`
		Type     string `json:"type"`
		CvssData struct {
			Version      string  `json:"version"`
			VectorString string  `json:"vectorString"`
			BaseScore    float64 `json:"baseScore"`
			BaseSeverity string  `json:"baseSeverity"`
		} `json:"cvssData"`
	} `json:"cvssMetricV31"`
	CvssMetricV30 []struct {
		Source   string `json:"source"`
		Type     string `json:"type"`
		CvssData struct {
			Version      string  `json:"version"`
			VectorString string  `json:"vectorString"`
			BaseScore    float64 `json:"baseScore"`
			BaseSeverity string  `json:"baseSeverity"`
		} `json:"cvssData"`
	} `json:"cvssMetricV30"`
	CvssMetricV2 []struct {
		Source   string `json:"source"`
		Type     string `json:"type"`
		CvssData struct {
			Version      string  `json:"version"`
			VectorString string  `json:"vectorString"`
			BaseScore    float64 `json:"baseScore"`
			BaseSeverity string  `json:"baseSeverity"`
		} `json:"cvssData"`
	} `json:"cvssMetricV2"`
}

type nvdReference struct {
	URL    string   `json:"url"`
	Source string   `json:"source"`
	Tags   []string `json:"tags"`
}

type nvdConfiguration struct {
	Nodes []struct {
		Operator string `json:"operator"`
		CpeMatch []struct {
			Vulnerable bool   `json:"vulnerable"`
			Criteria   string `json:"criteria"`
		} `json:"cpeMatch"`
	} `json:"nodes"`
}

// 创建NVD采集器
func NewNVDCrawler(apiKey string) Grabber {
	client := resty.New()
	client.SetHeader("User-Agent", "Vulnerability_Push/1.0")
	client.SetTimeout(30 * time.Second)
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)

	if apiKey != "" {
		client.SetHeader("apiKey", apiKey)
	}

	return &NVDCrawler{
		client:  client,
		apiKey:  apiKey,
		baseURL: "https://services.nvd.nist.gov/rest/json/cves/2.0",
	}
}

// 创建调试模式的NVD国家漏洞数据库采集器
// 注意：此函数仅供debug_main.go调试使用，不应在正常运行时调用
func NewNVDCrawlerWithDebug(apiKey string) Grabber {
	crawler := &NVDCrawler{
		client:  resty.New(),
		apiKey:  apiKey,
		baseURL: "https://services.nvd.nist.gov/rest/json/cves/2.0",
		debug:   true,
	}

	crawler.client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	crawler.client.SetHeader("Accept", "application/json")
	if apiKey != "" {
		crawler.client.SetHeader("apiKey", apiKey)
	}
	crawler.client.SetRetryCount(3)
	crawler.client.SetRetryWaitTime(5 * time.Second)
	crawler.client.SetTimeout(30 * time.Second)

	// 不启用详细日志 - 确保此功能保持禁用状态
	// crawler.client.SetDebug(true)

	return crawler
}

// 获取采集源信息
func (n *NVDCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "nvd",
		DisplayName: "美国国家漏洞数据库(NVD)",
		Link:        "https://nvd.nist.gov/",
	}
}

// 获取最新漏洞信息
func (n *NVDCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	if n.debug {
		logger.Infof("调试模式：NVD采集器开始执行")
		if startDate != "" || endDate != "" {
			logger.Infof("调试模式：日期范围过滤：开始日期=%s，结束日期=%s", startDate, endDate)
		}
	}

	// 如果没有提供日期范围，默认获取最近3天的漏洞
	if startDate == "" {
		startDate = time.Now().AddDate(0, 0, -3).Format("2006-01-02")
		if n.debug {
			logger.Infof("调试模式：未提供开始日期，默认使用3天前：%s", startDate)
		}
	}

	if endDate == "" {
		endDate = time.Now().Format("2006-01-02")
		if n.debug {
			logger.Infof("调试模式：未提供结束日期，默认使用当前日期：%s", endDate)
		}
	}

	// 转换为NVD API要求的时间格式
	pubStartDate := startDate + "T00:00:00.000"
	pubEndDate := endDate + "T23:59:59.999"

	// 构建URL
	url := fmt.Sprintf("%s?pubStartDate=%s&pubEndDate=%s&resultsPerPage=100", n.baseURL, pubStartDate, pubEndDate)

	if n.debug {
		logger.Infof("调试模式：请求URL：%s", url)
	}

	// 发起请求
	resp, err := n.client.R().Get(url)
	if err != nil {
		if n.debug {
			logger.Infof("调试模式：请求失败：%v", err)
		}
		return nil, fmt.Errorf("请求NVD API失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode() != 200 {
		if n.debug {
			logger.Infof("调试模式：NVD API返回错误状态码：%d", resp.StatusCode())
		}
		return nil, fmt.Errorf("NVD API返回错误状态码：%d", resp.StatusCode())
	}

	// 解析响应
	var nvdResp nvdResponse
	if err := json.Unmarshal(resp.Body(), &nvdResp); err != nil {
		if n.debug {
			logger.Infof("调试模式：解析响应失败：%v", err)
		}
		return nil, fmt.Errorf("解析NVD API响应失败: %v", err)
	}

	if n.debug {
		logger.Infof("调试模式：成功获取%d条漏洞信息", len(nvdResp.Vulnerabilities))
	}

	// 转换为VulnInfo
	var result []*VulnInfo
	for _, vuln := range nvdResp.Vulnerabilities {
		cve := vuln.Cve

		// 获取英文描述
		description := ""
		for _, desc := range cve.Descriptions {
			if desc.Lang == "en" {
				description = desc.Value
				break
			}
		}

		// 提取CVSS信息
		severity := SeverityLow
		cvssScore := ""
		cvssVector := ""

		if len(cve.Metrics.CvssMetricV31) > 0 {
			cvssData := cve.Metrics.CvssMetricV31[0].CvssData
			cvssScore = fmt.Sprintf("%.1f", cvssData.BaseScore)
			cvssVector = cvssData.VectorString
			severity = n.mapSeverity(cvssData.BaseSeverity)
		} else if len(cve.Metrics.CvssMetricV30) > 0 {
			cvssData := cve.Metrics.CvssMetricV30[0].CvssData
			cvssScore = fmt.Sprintf("%.1f", cvssData.BaseScore)
			cvssVector = cvssData.VectorString
			severity = n.mapSeverity(cvssData.BaseSeverity)
		} else if len(cve.Metrics.CvssMetricV2) > 0 {
			cvssData := cve.Metrics.CvssMetricV2[0].CvssData
			cvssScore = fmt.Sprintf("%.1f", cvssData.BaseScore)
			cvssVector = cvssData.VectorString
			severity = n.mapSeverity(cvssData.BaseSeverity)
		}

		// 提取参考链接
		var references []string
		for _, ref := range cve.References {
			references = append(references, ref.URL)
		}

		// 提取CWE信息
		cwe := ""
		if len(cve.WeaknessEnumeration) > 0 {
			for _, we := range cve.WeaknessEnumeration {
				if we.Source == "CWE" && len(we.WeaknessList) > 0 {
					cwe = we.WeaknessList[0].ID
					break
				}
			}
		}

		// 提取受影响的产品
		affectedProducts := n.extractAffectedProducts(cve.Configurations)

		// 构建标签
		tags := []string{"nvd"}
		if cve.ID != "" {
			tags = append(tags, cve.ID)
		}
		if cwe != "" {
			tags = append(tags, cwe)
		}

		// 解析发布日期，处理NVD的时间格式
		publishedDate := n.parseNvdDate(cve.Published)

		vulnInfo := &VulnInfo{
			UniqueKey:   cve.ID,
			Title:       cve.ID + " - " + n.truncateString(description, 100),
			Description: description,
			Severity:    severity,
			CVE:         cve.ID,
			CWE:         cwe,
			Score:       cvssScore,
			Disclosure:  publishedDate,
			References:  references,
			From:        "https://nvd.nist.gov/vuln/detail/" + cve.ID,
			Tags:        tags,
			VulnType:    strings.Join(affectedProducts, ","),
			Remediation: "请参考官方修复建议，更新受影响的软件版本。" + cvssVector,
		}

		result = append(result, vulnInfo)
	}

	if n.debug {
		logger.Infof("调试模式：成功处理%d条漏洞信息", len(result))
	}

	return result, nil
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (n *NVDCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 先获取所有漏洞信息
	allVulns, err := n.GetUpdate(ctx, pageLimit, startDate, endDate)
	if err != nil {
		return nil, err
	}

	if n.debug {
		logger.Infof("调试模式：开始检查漏洞是否已存在于数据库中")
	}

	// 筛选出不存在于数据库中的漏洞
	var results []*VulnInfo
	var existingCount int

	for _, vuln := range allVulns {
		if checkExists != nil && checkExists(vuln.UniqueKey) {
			existingCount++
			if n.debug {
				logger.Infof("调试模式：漏洞已存在于数据库中，跳过: %s (%s)", vuln.Title, vuln.UniqueKey)
			}
			continue
		}

		if n.debug {
			logger.Infof("调试模式：漏洞不存在于数据库中，添加: %s (%s)", vuln.Title, vuln.UniqueKey)
		}
		results = append(results, vuln)
	}

	if n.debug {
		logger.Infof("调试模式：已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	} else {
		logger.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(results))
	}

	return results, nil
}

// 判断漏洞是否值得关注
func (n *NVDCrawler) IsValuable(info *VulnInfo) bool {
	// 有CVE编号的漏洞
	if info.CVE != "" {
		return true
	}

	// 高危或严重的漏洞
	if info.Severity == SeverityHigh || info.Severity == SeverityCritical {
		return true
	}

	return false
}

// 将CVSS严重性映射到系统使用的严重性
func (n *NVDCrawler) mapSeverity(severity string) string {
	switch strings.ToUpper(severity) {
	case "CRITICAL":
		return SeverityCritical
	case "HIGH":
		return SeverityHigh
	case "MEDIUM":
		return SeverityMedium
	case "LOW":
		return SeverityLow
	default:
		return SeverityLow
	}
}

// 从CPE配置中提取受影响的产品
func (n *NVDCrawler) extractAffectedProducts(configs []nvdConfiguration) []string {
	var products []string
	productMap := make(map[string]bool)

	for _, config := range configs {
		for _, node := range config.Nodes {
			for _, cpe := range node.CpeMatch {
				if cpe.Vulnerable {
					// 解析CPE字符串
					parts := strings.Split(cpe.Criteria, ":")
					if len(parts) >= 5 {
						vendor := parts[3]
						product := parts[4]
						productString := fmt.Sprintf("%s:%s", vendor, product)
						if _, exists := productMap[productString]; !exists {
							productMap[productString] = true
							products = append(products, productString)
						}
					}
				}
			}
		}
	}

	return products
}

// 截断字符串
func (n *NVDCrawler) truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// 解析NVD日期格式
func (n *NVDCrawler) parseNvdDate(dateStr string) string {
	// NVD日期格式: "2025-06-04T00:15:25.333"
	// 我们需要转换为 "2025-06-04" 格式
	if dateStr == "" {
		return GetCurrentDate()
	}

	// 尝试解析日期
	t, err := time.Parse("2006-01-02T15:04:05.999", dateStr)
	if err != nil {
		// 尝试其他可能的格式
		t, err = time.Parse("2006-01-02T15:04:05", dateStr)
		if err != nil {
			if n.debug {
				logger.Infof("调试模式：无法解析日期 %s: %v", dateStr, err)
			}
			return GetCurrentDate()
		}
	}

	return t.Format("2006-01-02")
}
