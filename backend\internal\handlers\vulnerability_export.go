package handlers

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"

	"vulnerability_push/internal/models"
)

// ExportVulnerabilitiesRequest 按条件导出漏洞请求
type ExportVulnerabilitiesRequest struct {
	StartDate string   `form:"startDate"`
	EndDate   string   `form:"endDate"`
	Severity  []string `form:"severity"`
	Source    []string `form:"source"`
	Format    string   `form:"format" binding:"required,oneof=xlsx csv"`
}

// ExportVulnerabilitiesByIDsRequest 按ID导出漏洞请求
type ExportVulnerabilitiesByIDsRequest struct {
	IDs []uint `json:"ids" binding:"required"`
}

// ExportVulnerabilitiesByDateRequest 按日期导出漏洞请求
type ExportVulnerabilitiesByDateRequest struct {
	StartDate  string   `json:"startDate" binding:"required"`
	EndDate    string   `json:"endDate" binding:"required"`
	Severities []string `json:"severities"`
}

// ExportVulnerabilities 按ID导出漏洞
func (h *VulnerabilityHandler) ExportVulnerabilities(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 检查请求方法，如果是POST则按ID导出，如果是GET则按条件导出
	if c.Request.Method == "POST" {
		h.exportVulnerabilitiesByIDs(c)
	} else {
		h.exportVulnerabilitiesByCondition(c)
	}
}

// exportVulnerabilitiesByIDs 按ID导出漏洞
func (h *VulnerabilityHandler) exportVulnerabilitiesByIDs(c *gin.Context) {
	var req ExportVulnerabilitiesByIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	if len(req.IDs) == 0 {
		h.BadRequest(c, "请选择要导出的漏洞")
		return
	}

	// 查询指定ID的漏洞
	var vulnerabilities []models.Vulnerability
	if err := h.db.Where("id IN ?", req.IDs).Find(&vulnerabilities).Error; err != nil {
		h.InternalServerError(c, "查询漏洞失败: "+err.Error())
		return
	}

	if len(vulnerabilities) == 0 {
		h.BadRequest(c, "未找到指定的漏洞")
		return
	}

	// 生成Excel文件
	filename, err := h.generateVulnerabilityExcel(vulnerabilities)
	if err != nil {
		h.InternalServerError(c, "生成Excel文件失败: "+err.Error())
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	
	// 返回文件
	c.File(filepath.Join("./exports", filename))
}

// exportVulnerabilitiesByCondition 按条件导出漏洞
func (h *VulnerabilityHandler) exportVulnerabilitiesByCondition(c *gin.Context) {
	var req ExportVulnerabilitiesRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 实现按条件导出逻辑
	h.Success(c, map[string]interface{}{
		"filename": "vulnerabilities_export.xlsx",
		"message":  "导出任务已创建，请稍后下载",
	})
}

// ExportVulnerabilitiesByDate 按日期导出漏洞
func (h *VulnerabilityHandler) ExportVulnerabilitiesByDate(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req ExportVulnerabilitiesByDateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 构建查询条件
	query := h.db.Model(&models.Vulnerability{})
	
	// 日期范围过滤
	query = query.Where("disclosure_date >= ? AND disclosure_date <= ?", req.StartDate, req.EndDate)
	
	// 严重程度过滤
	if len(req.Severities) > 0 {
		query = query.Where("severity IN ?", req.Severities)
	}

	// 查询漏洞数据
	var vulnerabilities []models.Vulnerability
	if err := query.Find(&vulnerabilities).Error; err != nil {
		h.InternalServerError(c, "查询漏洞失败: "+err.Error())
		return
	}

	if len(vulnerabilities) == 0 {
		h.BadRequest(c, "指定日期范围内没有漏洞数据")
		return
	}

	// 生成Excel文件
	filename, err := h.generateVulnerabilityExcelByDate(vulnerabilities, req.StartDate, req.EndDate)
	if err != nil {
		h.InternalServerError(c, "生成Excel文件失败: "+err.Error())
		return
	}

	// 设置响应头
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	
	// 返回文件
	c.File(filepath.Join("./exports", filename))
}

// generateVulnerabilityExcel 生成漏洞Excel文件
func (h *VulnerabilityHandler) generateVulnerabilityExcel(vulnerabilities []models.Vulnerability) (string, error) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	sheetName := "漏洞列表"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return "", fmt.Errorf("创建工作表失败: %v", err)
	}

	// 设置表头
	headers := []string{
		"ID", "漏洞名称", "漏洞编号", "严重程度", "披露日期", 
		"来源", "标签", "描述", "修复建议", "参考链接", "创建时间",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return "", fmt.Errorf("设置表头失败: %v", err)
		}
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
	})
	if err != nil {
		return "", fmt.Errorf("创建表头样式失败: %v", err)
	}

	// 应用表头样式
	if err := f.SetRowStyle(sheetName, 1, 1, headerStyle); err != nil {
		return "", fmt.Errorf("应用表头样式失败: %v", err)
	}

	// 写入数据
	for i, vuln := range vulnerabilities {
		row := i + 2 // 从第2行开始（第1行是表头）
		
		// 格式化创建时间
		createdAt := time.Unix(vuln.CreatedAt, 0).Format("2006-01-02 15:04:05")
		
		// 处理标签和参考链接
		tags := strings.ReplaceAll(vuln.Tags, ",", ", ")
		references := strings.ReplaceAll(vuln.References, ",", ", ")
		
		data := []interface{}{
			vuln.ID,
			vuln.Name,
			vuln.VulnID,
			vuln.Severity,
			vuln.DisclosureDate,
			vuln.Source,
			tags,
			vuln.Description,
			vuln.Remediation,
			references,
			createdAt,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			if err := f.SetCellValue(sheetName, cell, value); err != nil {
				return "", fmt.Errorf("设置单元格值失败: %v", err)
			}
		}
	}

	// 设置列宽
	columnWidths := map[string]float64{
		"A": 8,  // ID
		"B": 30, // 漏洞名称
		"C": 20, // 漏洞编号
		"D": 12, // 严重程度
		"E": 15, // 披露日期
		"F": 15, // 来源
		"G": 20, // 标签
		"H": 40, // 描述
		"I": 40, // 修复建议
		"J": 30, // 参考链接
		"K": 20, // 创建时间
	}

	for col, width := range columnWidths {
		if err := f.SetColWidth(sheetName, col, col, width); err != nil {
			return "", fmt.Errorf("设置列宽失败: %v", err)
		}
	}

	// 创建导出目录（如果不存在）
	exportDir := "./exports"
	if _, err := os.Stat(exportDir); os.IsNotExist(err) {
		if err := os.MkdirAll(exportDir, 0755); err != nil {
			return "", fmt.Errorf("创建导出目录失败: %v", err)
		}
	}

	// 生成文件名（使用时间戳避免重复）
	timeStamp := time.Now().Unix()
	filename := fmt.Sprintf("漏洞导出_%d.xlsx", timeStamp)
	filePath := filepath.Join(exportDir, filename)

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 保存Excel文件
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("保存Excel文件失败: %v", err)
	}

	return filename, nil
}

// generateVulnerabilityExcelByDate 按日期生成漏洞Excel文件
func (h *VulnerabilityHandler) generateVulnerabilityExcelByDate(vulnerabilities []models.Vulnerability, startDate, endDate string) (string, error) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	sheetName := "漏洞列表"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return "", fmt.Errorf("创建工作表失败: %v", err)
	}

	// 设置表头
	headers := []string{
		"ID", "漏洞名称", "漏洞编号", "严重程度", "披露日期",
		"来源", "标签", "描述", "修复建议", "参考链接", "创建时间",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return "", fmt.Errorf("设置表头失败: %v", err)
		}
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
	})
	if err != nil {
		return "", fmt.Errorf("创建表头样式失败: %v", err)
	}

	// 应用表头样式
	if err := f.SetRowStyle(sheetName, 1, 1, headerStyle); err != nil {
		return "", fmt.Errorf("应用表头样式失败: %v", err)
	}

	// 写入数据
	for i, vuln := range vulnerabilities {
		row := i + 2 // 从第2行开始（第1行是表头）

		// 格式化创建时间
		createdAt := time.Unix(vuln.CreatedAt, 0).Format("2006-01-02 15:04:05")

		// 处理标签和参考链接
		tags := strings.ReplaceAll(vuln.Tags, ",", ", ")
		references := strings.ReplaceAll(vuln.References, ",", ", ")

		data := []interface{}{
			vuln.ID,
			vuln.Name,
			vuln.VulnID,
			vuln.Severity,
			vuln.DisclosureDate,
			vuln.Source,
			tags,
			vuln.Description,
			vuln.Remediation,
			references,
			createdAt,
		}

		for j, value := range data {
			cell := fmt.Sprintf("%c%d", 'A'+j, row)
			if err := f.SetCellValue(sheetName, cell, value); err != nil {
				return "", fmt.Errorf("设置单元格值失败: %v", err)
			}
		}
	}

	// 为描述和修复建议列设置更宽的宽度
	f.SetColWidth(sheetName, "H", "H", 40)
	f.SetColWidth(sheetName, "J", "J", 40)

	// 创建导出目录（如果不存在）
	exportDir := "./exports"
	if _, err := os.Stat(exportDir); os.IsNotExist(err) {
		if err := os.MkdirAll(exportDir, 0755); err != nil {
			return "", fmt.Errorf("创建导出目录失败: %v", err)
		}
	}

	// 生成文件名（使用时间戳避免重复）
	timeStamp := time.Now().Unix()
	filename := fmt.Sprintf("漏洞导出_%s_%s_%d.xlsx", startDate, endDate, timeStamp)
	filePath := filepath.Join(exportDir, filename)

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 保存Excel文件
	if err := f.SaveAs(filePath); err != nil {
		return "", fmt.Errorf("保存Excel文件失败: %v", err)
	}

	return filename, nil
}
