package handlers

import (
	"vulnerability_push/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserHandler 用户处理器
type UserHandler struct {
	*BaseHandler
	authService *service.AuthService
	userService service.UserServiceInterface
}

// NewUserHandler 创建用户处理器
func NewUserHandler(db *gorm.DB, jwtSecret string, jwtExpireHours int) *UserHandler {
	return &UserHandler{
		BaseHandler: NewBaseHandler(),
		authService: service.NewAuthService(db, jwtSecret, jwtExpireHours),
		userService: service.NewUserService(db),
	}
}

// 登录相关

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string      `json:"token"`
	User  interface{} `json:"user"`
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用认证服务进行登录
	response, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		h.BadRequest(c, err.Error())
		return
	}

	h.SuccessWithMessage(c, "登录成功", response)
}

// Logout 用户登出
func (h *UserHandler) Logout(c *gin.Context) {
	// TODO: 实现登出逻辑（如果需要的话）
	h.SuccessWithMessage(c, "登出成功", nil)
}

// VerifyToken 验证令牌
func (h *UserHandler) VerifyToken(c *gin.Context) {
	// TODO: 实现令牌验证逻辑
	// token := h.GetAuthToken(c)
	// if token == "" {
	//     h.Unauthorized(c, "令牌缺失")
	//     return
	// }

	// 验证令牌有效性
	// valid, err := h.authService.VerifyToken(token)
	// if err != nil || !valid {
	//     h.Unauthorized(c, "令牌无效")
	//     return
	// }

	// 临时响应
	h.SuccessWithMessage(c, "令牌有效", map[string]interface{}{
		"valid": true,
	})
}

// GetCurrentUserInfo 获取当前用户信息
func (h *UserHandler) GetCurrentUserInfo(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	userID, username, role, exists := h.GetCurrentUser(c)
	if !exists {
		h.Unauthorized(c, "获取用户信息失败")
		return
	}

	// 返回用户信息
	user := map[string]interface{}{
		"id":       userID,
		"username": username,
		"email":    username + "@example.com",
		"role":     role,
		"status":   true,
	}

	response := map[string]interface{}{
		"user": user,
	}

	h.SuccessWithMessage(c, "获取用户信息成功", response)
}

// 用户管理

// GetUsersRequest 获取用户列表请求
type GetUsersRequest struct {
	Page     int    `form:"page" binding:"required,min=1"`
	PageSize int    `form:"pageSize" binding:"required,min=5,max=100"`
	Username string `form:"username"`
	Role     string `form:"role"`
	Status   *bool  `form:"status"`
}

// GetUsers 获取用户列表
func (h *UserHandler) GetUsers(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	var req GetUsersRequest
	if err := h.BindQuery(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务获取用户列表
	result, err := h.userService.GetUsers(c.Request.Context(), &service.GetUsersRequest{
		PaginationRequest: service.PaginationRequest{
			Page:     req.Page,
			PageSize: req.PageSize,
		},
		Username: req.Username,
		Role:     req.Role,
		Status:   req.Status,
	})
	if err != nil {
		h.InternalServerError(c, "获取用户列表失败: "+err.Error())
		return
	}

	h.PaginatedSuccess(c, result.Users, result.Total, req.Page, req.PageSize)
}

// GetUser 获取单个用户
func (h *UserHandler) GetUser(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务获取用户详情
	user, err := h.userService.GetUserByID(c.Request.Context(), id)
	if err != nil {
		h.NotFound(c, "用户不存在")
		return
	}

	h.Success(c, user)
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"required,email"`
	Role     string `json:"role" binding:"required,oneof=admin user"`
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	var req CreateUserRequest
	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务创建用户
	user, err := h.userService.CreateUser(c.Request.Context(), &service.CreateUserRequest{
		Username: req.Username,
		Password: req.Password,
		Email:    req.Email,
		Role:     req.Role,
	})
	if err != nil {
		h.BadRequest(c, err.Error())
		return
	}

	h.SuccessWithMessage(c, "用户创建成功", user)
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email  string `json:"email" binding:"omitempty,email"`
	Role   string `json:"role" binding:"omitempty,oneof=admin user"`
	Status *bool  `json:"status"`
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdateUserRequest
	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务更新用户
	err = h.userService.UpdateUser(c.Request.Context(), id, &service.UpdateUserRequest{
		Email:  req.Email,
		Role:   req.Role,
		Status: req.Status,
	})
	if err != nil {
		h.BadRequest(c, err.Error())
		return
	}

	h.SuccessWithMessage(c, "用户更新成功", nil)
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务删除用户
	err = h.userService.DeleteUser(c.Request.Context(), id)
	if err != nil {
		h.BadRequest(c, err.Error())
		return
	}

	h.SuccessWithMessage(c, "用户删除成功", nil)
}

// 密码管理

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=6"`
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	_, _ = h.GetCurrentUserID(c)

	var req ChangePasswordRequest
	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 调用用户服务修改密码
	// err := h.userService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword)
	// if err != nil {
	//     h.BadRequest(c, err.Error())
	//     return
	// }

	h.SuccessWithMessage(c, "密码修改成功", nil)
}

// ResetAPIKey 重置当前用户的API密钥
func (h *UserHandler) ResetAPIKey(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	userID, exists := h.GetCurrentUserID(c)
	if !exists {
		h.Unauthorized(c, "获取用户信息失败")
		return
	}

	// 调用用户服务重置API密钥
	apiKey, err := h.userService.ResetAPIKey(c.Request.Context(), userID)
	if err != nil {
		h.InternalServerError(c, "重置API密钥失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "API密钥重置成功", map[string]interface{}{
		"apiKey": apiKey,
	})
}

// AdminResetAPIKey 管理员重置指定用户的API密钥
func (h *UserHandler) AdminResetAPIKey(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务重置API密钥
	apiKey, err := h.userService.ResetAPIKey(c.Request.Context(), id)
	if err != nil {
		h.InternalServerError(c, "重置API密钥失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "API密钥重置成功", map[string]interface{}{
		"apiKey": apiKey,
	})
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	userID, username, role, exists := h.GetCurrentUser(c)
	if !exists {
		h.Unauthorized(c, "获取用户信息失败")
		return
	}

	// TODO: 调用用户服务获取完整用户信息
	// user, err := h.userService.GetUserByID(c.Request.Context(), userID)
	// if err != nil {
	//     h.InternalServerError(c, "获取用户信息失败: "+err.Error())
	//     return
	// }

	// 临时响应
	profile := map[string]interface{}{
		"id":       userID,
		"username": username,
		"role":     role,
		"email":    "<EMAIL>",
		"status":   true,
	}

	h.Success(c, profile)
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	_, _ = h.GetCurrentUserID(c)

	var req struct {
		Email string `json:"email" binding:"omitempty,email"`
	}

	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 调用用户服务更新用户资料
	// err := h.userService.UpdateUser(c.Request.Context(), userID, &service.UpdateUserRequest{
	//     Email: req.Email,
	// })
	// if err != nil {
	//     h.BadRequest(c, err.Error())
	//     return
	// }

	h.SuccessWithMessage(c, "资料更新成功", nil)
}

// AdminChangePassword 管理员修改用户密码
func (h *UserHandler) AdminChangePassword(c *gin.Context) {
	// 检查管理员权限
	if !h.RequireAdmin(c) {
		return
	}

	_, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req struct {
		NewPassword string `json:"newPassword" binding:"required,min=6"`
	}

	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// TODO: 调用用户服务修改密码
	// err = h.userService.AdminChangePassword(c.Request.Context(), id, req.NewPassword)
	// if err != nil {
	//     h.BadRequest(c, err.Error())
	//     return
	// }

	h.SuccessWithMessage(c, "密码修改成功", nil)
}

// ValidateAPIKey 验证API密钥
func (h *UserHandler) ValidateAPIKey(c *gin.Context) {
	var req struct {
		APIKey string `json:"apiKey" binding:"required"`
	}

	if err := h.BindJSON(c, &req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用用户服务验证API密钥
	user, err := h.userService.ValidateAPIKey(c.Request.Context(), req.APIKey)
	if err != nil {
		h.BadRequest(c, "验证API密钥失败: "+err.Error())
		return
	}

	h.SuccessWithMessage(c, "API密钥验证成功", map[string]interface{}{
		"valid": true,
		"user": map[string]interface{}{
			"id":       user.ID,
			"username": user.Username,
			"role":     user.Role,
		},
	})
}
