-- IOC情报表推送状态字段迁移脚本
-- 将target_org字段替换为push_status和pushed_at字段
-- 执行前请备份数据库

USE vuln_push;

-- 1. 检查当前表结构
SHOW COLUMNS FROM ioc_intelligence;

-- 2. 添加新字段（如果不存在）
-- 添加push_status字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ioc_intelligence' 
     AND column_name = 'push_status') = 0,
    'ALTER TABLE ioc_intelligence ADD COLUMN push_status VARCHAR(20) DEFAULT "not_pushed" COMMENT "推送状态：not_pushed, pushed, failed"',
    'SELECT "push_status字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加pushed_at字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'ioc_intelligence' 
     AND column_name = 'pushed_at') = 0,
    'ALTER TABLE ioc_intelligence ADD COLUMN pushed_at BIGINT DEFAULT 0 COMMENT "推送时间，0表示未推送"',
    'SELECT "pushed_at字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 根据推送记录更新推送状态
-- 查找有推送记录的IOC情报，更新其推送状态和时间
UPDATE ioc_intelligence i
SET 
    push_status = (
        SELECT CASE 
            WHEN COUNT(CASE WHEN r.status = 'success' THEN 1 END) > 0 THEN 'pushed'
            WHEN COUNT(CASE WHEN r.status = 'failed' THEN 1 END) > 0 THEN 'failed'
            ELSE 'not_pushed'
        END
        FROM ioc_intelligence_records r 
        WHERE r.ioc_intelligence_id = i.id
    ),
    pushed_at = (
        SELECT COALESCE(MAX(r.pushed_at), 0)
        FROM ioc_intelligence_records r 
        WHERE r.ioc_intelligence_id = i.id AND r.status = 'success'
    )
WHERE EXISTS (
    SELECT 1 FROM ioc_intelligence_records r 
    WHERE r.ioc_intelligence_id = i.id
);

-- 4. 删除旧的target_org字段（可选，建议先测试确认无问题后再执行）
-- ALTER TABLE ioc_intelligence DROP COLUMN target_org;

-- 5. 验证迁移结果
SELECT 
    push_status,
    COUNT(*) as count
FROM ioc_intelligence 
GROUP BY push_status;

SELECT 
    COUNT(*) as total_count,
    COUNT(CASE WHEN pushed_at > 0 THEN 1 END) as pushed_count,
    COUNT(CASE WHEN push_status = 'pushed' THEN 1 END) as status_pushed_count
FROM ioc_intelligence;

-- 6. 显示最近推送的记录
SELECT 
    id,
    ioc,
    push_status,
    FROM_UNIXTIME(pushed_at) as pushed_time
FROM ioc_intelligence 
WHERE pushed_at > 0 
ORDER BY pushed_at DESC 
LIMIT 10;
