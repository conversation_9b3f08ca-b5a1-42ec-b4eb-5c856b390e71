# 数据接口功能架构说明

## 概述

数据接口功能已经重构为模块化、可扩展的架构，支持多种数据源的统一管理和执行。新架构采用注册器模式，便于添加新的数据接口类型。

## 架构组件

### 1. 核心组件

#### DataInterfaceHandler (`data_interface_handler.go`)
- **职责**: 主要的HTTP请求处理器
- **功能**: 处理数据接口的CRUD操作、执行控制、统计查询等
- **特点**: 集成了注册器和管理器，提供统一的API接口

#### DataInterfaceRegistry (`data_interface_registry.go`)
- **职责**: 数据接口类型注册和管理
- **功能**: 注册、获取、验证数据接口执行器
- **特点**: 线程安全，支持动态接口类型管理

#### DataInterfaceManager (`data_interface_manager.go`)
- **职责**: 数据接口执行生命周期管理
- **功能**: 执行状态跟踪、并发控制、统计信息管理
- **特点**: 防止重复执行，提供详细的执行统计

#### DataInterfaceFactory (`data_interface_factory.go`)
- **职责**: 数据接口实例创建工厂
- **功能**: 创建接口实例、提供元数据信息
- **特点**: 支持接口类型验证和元数据查询

### 2. 基础组件

#### BaseDataInterface (`data_interface_base.go`)
- **职责**: 数据接口基类，提供通用功能
- **功能**: IP地址处理、白名单检查、数据验证、风险评分等
- **特点**: 可复用的工具方法，减少代码重复

#### DataInterfaceExecutor (接口)
- **职责**: 数据接口执行器接口定义
- **方法**:
  - `Execute()`: 执行数据采集
  - `ValidateConfig()`: 验证配置
  - `GetConfigSchema()`: 获取配置模式
  - `GetType()`: 获取接口类型
  - `GetDescription()`: 获取接口描述

### 3. 具体实现

#### CCCCBlackTechInterface (`data_interface_cccc_impl.go`)
- **职责**: CCCC黑科技数据接口具体实现
- **功能**: API调用、数据处理、去重合并、IOC生成
- **特点**: 支持UUID去重、攻击流合并、目标组织过滤

## API接口

### 数据接口管理
```
GET    /admin/data-interfaces              # 获取数据接口列表
POST   /admin/data-interfaces              # 创建数据接口
GET    /admin/data-interfaces/stats        # 获取统计信息
GET    /admin/data-interfaces/running      # 获取运行中的接口
GET    /admin/data-interfaces/:id          # 获取接口详情
PUT    /admin/data-interfaces/:id          # 更新接口
DELETE /admin/data-interfaces/:id          # 删除接口
POST   /admin/data-interfaces/:id/toggle   # 切换接口状态
POST   /admin/data-interfaces/:id/run      # 手动执行接口
GET    /admin/data-interfaces/:id/logs     # 获取执行日志
GET    /admin/data-interfaces/:id/stats    # 获取详细统计
```

### 接口类型管理
```
GET    /admin/data-interfaces/types        # 获取所有接口类型
GET    /admin/data-interfaces/types/:type  # 获取特定类型信息
POST   /admin/data-interfaces/validate-config # 验证配置
```

## 数据流程

### 1. 接口注册流程
```
1. 实现 DataInterfaceExecutor 接口
2. 在 registerBuiltinInterfaces() 中注册
3. 在 DataInterfaceFactory 中添加创建逻辑
4. 系统自动识别和管理新接口类型
```

### 2. 数据采集流程
```
1. 调度器触发或手动执行
2. DataInterfaceManager 检查执行状态
3. 获取对应的 DataInterfaceExecutor
4. 解析和验证配置
5. 执行数据采集
6. 处理和保存数据
7. 更新执行日志和统计
```

### 3. 数据处理流程 (以CCCC为例)
```
1. API调用获取原始数据
2. UUID去重过滤
3. 内网流量过滤
4. 白名单过滤
5. 目标组织过滤
6. 攻击流合并
7. 转换为IOC源数据
8. 批量保存到数据库
```

## 配置说明

### CCCC黑科技接口配置
```json
{
  "host": "api.example.com",
  "user_key": "your-api-key",
  "target_organizations": ["org1", "org2"]
}
```

### 配置模式 (Schema)
每个接口类型都提供JSON Schema格式的配置模式，用于：
- 前端动态生成配置表单
- 后端配置验证
- 文档生成

## 扩展指南

### 添加新的数据接口类型

1. **创建接口实现**
```go
type NewInterface struct {
    *BaseDataInterface
    handler *DataInterfaceHandler
}

func (n *NewInterface) Execute(dataInterface *models.DataInterface, config map[string]interface{}, log *models.DataInterfaceLog) error {
    // 实现具体的数据采集逻辑
}

func (n *NewInterface) ValidateConfig(config map[string]interface{}) error {
    // 实现配置验证逻辑
}

func (n *NewInterface) GetConfigSchema() map[string]interface{} {
    // 返回配置模式
}
```

2. **注册接口**
```go
// 在 registerBuiltinInterfaces() 中添加
h.registry.Register("new_interface", NewNewInterface(h))
```

3. **更新工厂**
```go
// 在 DataInterfaceFactory.CreateInterface() 中添加
case "new_interface":
    return NewNewInterface(f.handler), nil
```

## 优势特点

### 1. 模块化设计
- 职责分离，每个组件专注特定功能
- 易于测试和维护
- 支持独立开发和部署

### 2. 可扩展性
- 注册器模式支持动态添加接口类型
- 统一的接口定义便于标准化
- 配置模式支持前端动态表单生成

### 3. 健壮性
- 并发控制防止重复执行
- 详细的错误处理和日志记录
- 配置验证确保数据质量

### 4. 性能优化
- 批量数据处理
- UUID去重减少重复处理
- 异步执行不阻塞用户操作

### 5. 监控和统计
- 实时执行状态跟踪
- 详细的统计信息
- 执行日志完整记录

## 注意事项

1. **线程安全**: 所有组件都考虑了并发访问的安全性
2. **资源管理**: 执行状态会自动清理，防止内存泄漏
3. **错误处理**: 完善的错误处理机制，确保系统稳定性
4. **配置验证**: 严格的配置验证，防止运行时错误
5. **日志记录**: 详细的日志记录，便于问题排查和性能分析

## 文件结构

```
backend/internal/handlers/
├── data_interface_handler.go      # 主处理器
├── data_interface_registry.go     # 接口注册器
├── data_interface_manager.go      # 执行管理器
├── data_interface_factory.go      # 接口工厂
├── data_interface_base.go         # 基础类
├── data_interface_cccc_impl.go    # CCCC接口实现
└── DATA_INTERFACE_README.md       # 本文档
```

这个新架构为后续添加更多数据接口类型提供了坚实的基础，同时保持了代码的清晰性和可维护性。

## 重构完成总结

### ✅ 已完成的优化

1. **架构重构**
   - 采用注册器模式，支持动态接口类型管理
   - 模块化设计，职责分离清晰
   - 统一的接口定义和执行流程

2. **代码结构优化**
   - 删除冗余文件：`data_interface_cccc.go`、`data_interface_processor.go`、`data_interface_utils.go`
   - 创建新的模块化文件结构
   - 提供完整的文档说明

3. **去重逻辑修复**
   - 修复了重构后的UUID去重逻辑
   - 恢复了攻击流合并功能
   - 正确处理UUID列表的保存

4. **数据库兼容性修复**
   - 修复了白名单查询的字段名问题
   - 适配了IOCIntelligenceData模型结构
   - 修复了字段类型转换问题

5. **API请求修复**
   - 修复了CCCC黑科技API请求结构
   - 恢复了完整的请求字段
   - 修正了JSON字段名称

6. **定时采集功能**
   - 调度器功能完整保留
   - 支持多种采集频率配置
   - 自动管理接口生命周期

### 🔧 关键修复点

1. **去重逻辑**
   ```go
   // 修复前：简单的slice返回
   func mergeAttackDataWithUUIDDedup(...) ([]AttackSummary, ...)

   // 修复后：map返回，支持UUID列表
   func mergeAttackDataWithUUIDDedup(...) (map[string]*AttackSummary, ...)
   ```

2. **AttackSummary结构**
   ```go
   // 新增字段支持完整的攻击流合并
   type AttackSummary struct {
       UUIDs        []string          // UUID列表
       Categories   map[string]int    // 类别统计
       Severities   map[string]int    // 严重程度统计
       IOCs         map[string]int    // IOC统计
       // ... 其他字段
   }
   ```

3. **白名单查询**
   ```go
   // 修复前：错误的字段名
   Where("ioc_value = ?", ip)

   // 修复后：正确的字段名
   Where("ioc = ?", ip)
   ```

### 📊 性能优化

1. **批量处理**：支持批量保存IOC数据
2. **并发控制**：防止重复执行同一接口
3. **内存优化**：使用map进行攻击流合并，减少重复计算
4. **数据库优化**：批量插入和查询优化

### 🚀 扩展能力

新架构支持轻松添加新的数据接口类型：

1. 实现`DataInterfaceExecutor`接口
2. 在注册器中注册新类型
3. 在工厂中添加创建逻辑
4. 系统自动识别和管理

### 📝 使用说明

1. **手动执行**：通过API `/admin/data-interfaces/:id/run` 手动触发
2. **定时执行**：通过调度器自动按配置频率执行
3. **状态监控**：通过API获取执行状态和统计信息
4. **日志查看**：完整的执行日志记录和查询

重构后的数据接口功能更加稳定、可扩展，并且保持了原有的所有功能特性。
