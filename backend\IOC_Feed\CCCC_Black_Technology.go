package IOC_Feed

import (
	"crypto/tls"
	"fmt"
	"net"
	"time"

	"github.com/go-resty/resty/v2"
)

// SearchDataRequest 搜索数据请求结构
type SearchDataRequest struct {
	Severity         string   `json:"severity"`
	Category         string   `json:"category"`
	SrcIP           string   `json:"src_ip"`
	DstIP           string   `json:"dst_ip"`
	EmailFrom       string   `json:"email_from"`
	To              string   `json:"to"`
	AttackStatus    string   `json:"attack_status"`
	AppProto        string   `json:"app_proto"`
	Method          string   `json:"method"`
	StatusCode      string   `json:"status_code"`
	MD5             string   `json:"md5"`
	XFF             string   `json:"xff"`
	Domain          string   `json:"domain"`
	Username        string   `json:"username"`
	VisitDirection  string   `json:"visit_direction"`
	IOC             string   `json:"ioc"`
	AppendQuery     string   `json:"appendQuery"`
	Start           int64    `json:"start"`
	End             int64    `json:"end"`
	QuickQuery      string   `json:"quickQuery"`
	FilterQuery     string   `json:"filterQuery"`
	IsCollectHistory bool    `json:"isCollectHistory"`
	QueryFields     []string `json:"queryFields"`
	From            int      `json:"from"`
	Size            int      `json:"size"`
	IsDeal          string   `json:"is_deal"`
	Sort            string   `json:"sort"`
	Order           string   `json:"order"`
	PageQueryMode   string   `json:"pageQueryMode"`
	Groups          []string `json:"groups"`
	TableName       string   `json:"tableName"`
}

// SearchDataRecord 搜索数据记录结构
type SearchDataRecord struct {
	StatusCode       string        `json:"status_code"`
	SrcIPCity       string        `json:"src_ip_city"`
	RID             string        `json:"rid"`
	UUID            string        `json:"uuid"`
	Platform        string        `json:"platform"`
	DstIP           string        `json:"dst_ip"`
	SrcIP           string        `json:"src_ip"`
	IsRead          string        `json:"is_read"`
	IsCollect       string        `json:"is_collect"`
	DstIPCity       string        `json:"dst_ip_city"`
	DstIPIsHost     string        `json:"dst_ip_isHost"`
	Host            string        `json:"host"`
	Victim          string        `json:"victim"`
	DealFlag        int           `json:"deal_flag"`
	Timestamp       int64         `json:"timestamp"`
	MaxInsertTime   int64         `json:"max_insert_time"`
	Severity        string        `json:"severity"`
	User1           string        `json:"user1"`
	AttackStatus    string        `json:"attack_status"`
	Method          string        `json:"method"`
	Attacker        string        `json:"attacker"`
	DstIPCountry    string        `json:"dst_ip_country"`
	URI             string        `json:"uri"`
	AppProto        string        `json:"app_proto"`
	TimestampCN     string        `json:"timestamp_cn"`
	IsDeal          string        `json:"is_deal"`
	ReadUser        []interface{} `json:"read_user"`
	DstPort         int           `json:"dst_port"`
	CollectUser     []interface{} `json:"collect_user"`
	SrcIPCountry    string        `json:"src_ip_country"`
	Category        string        `json:"category"`
	IOC             string        `json:"ioc"`
	SrcIPIsHost     string        `json:"src_ip_isHost"`
	ServerName      string        `json:"server_name"`
	QTypeName       string        `json:"qtype_name"`
	Query           string        `json:"query"`
}

// SearchDataResponse 搜索数据响应结构
type SearchDataResponse struct {
	Total           int                `json:"total"`
	Size            int                `json:"size"`
	Current         int                `json:"current"`
	Page            int                `json:"page"`
	Records         []SearchDataRecord `json:"records"`
	SearchHistoryID interface{}        `json:"searchHistoryId"`
}

// isPrivateIP 函数已在IP_Search.go中定义，这里不再重复定义

// isInternalTraffic 判断是否为内网流量（源IP和目标IP都是内网地址）
func isInternalTraffic(record SearchDataRecord) bool {
	return IsPrivateIP(record.SrcIP) && IsPrivateIP(record.DstIP)
}

// IOC白名单列表
var iocWhitelist = map[string]bool{
	"在流量中检测到远程控制工具Todesk": true,
	"流量中检测到远控工具Sunlogin": true,
	"在响应数据中发现Tomcat管理后台": true,
	// 可以根据需要添加更多白名单IOC
}

// isIOCWhitelisted 检查IOC是否在白名单中
func isIOCWhitelisted(ioc string) bool {
	if ioc == "" {
		return false
	}
	return iocWhitelist[ioc]
}

// addToIOCWhitelist 添加IOC到白名单
func addToIOCWhitelist(ioc string) {
	iocWhitelist[ioc] = true
}

// removeFromIOCWhitelist 从白名单移除IOC
func removeFromIOCWhitelist(ioc string) {
	delete(iocWhitelist, ioc)
}

// isRecordFiltered 检查记录是否应该被过滤（内网流量或白名单IOC）
func isRecordFiltered(record SearchDataRecord) bool {
	// 过滤内网流量
	if isInternalTraffic(record) {
		return true
	}

	// 过滤白名单IOC
	if isIOCWhitelisted(record.IOC) {
		return true
	}

	return false
}

// isTargetOrganization 检查IP是否属于目标组织（通信中心或交通运输部）
func isTargetOrganization(ip string) bool {
	label := getSourceLabel(ip)
	return label == "通信中心" || label == "交通运输部"
}

// shouldIncludeAttackFlow 检查攻击流是否应该包含在结果中
func shouldIncludeAttackFlow(srcIP, dstIP string) bool {
	// 只显示涉及通信中心或交通运输部的攻击流
	return isTargetOrganization(srcIP) || isTargetOrganization(dstIP)
}

// IOCIntelligenceData IOC情报数据模型
type IOCIntelligenceData struct {
	ID               uint   `gorm:"primaryKey" json:"id"`
	AttackIP         string `gorm:"size:45;not null" json:"attack_ip"`         // 攻击IP
	VictimIP         string `gorm:"size:45;not null" json:"victim_ip"`         // 受害IP
	SourceLabel      string `gorm:"size:50" json:"source_label"`               // 来源标签
	Category         string `gorm:"size:100" json:"category"`                  // 攻击类别
	AttackCount      int    `gorm:"not null;default:0" json:"attack_count"`    // 攻击次数
	FirstAttackTime  string `gorm:"size:20" json:"first_attack_time"`          // 首次攻击时间
	LastAttackTime   string `gorm:"size:20" json:"last_attack_time"`           // 最后攻击时间
	ThreatScore      float64 `gorm:"type:decimal(4,2);default:0" json:"threat_score"` // 威胁评分
	CreatedAt        string `gorm:"size:20" json:"created_at"`                 // 创建时间
	UpdatedAt        string `gorm:"size:20" json:"updated_at"`                 // 更新时间
}

// TableName 指定表名
func (IOCIntelligenceData) TableName() string {
	return "ioc_intelligence_data"
}

// calculateSimpleThreatScore 计算简化威胁评分（只基于危害等级和攻击次数）
func calculateSimpleThreatScore(severities map[string]int, attackCount int) float64 {
	// 基础分数：根据最高严重程度计算
	var baseScore float64 = 0
	for severity := range severities {
		var score float64
		switch severity {
		case "高危":
			score = 8.0
		case "中危":
			score = 5.0
		case "低危":
			score = 2.0
		case "信息":
			score = 1.0
		default:
			score = 3.0
		}
		if score > baseScore {
			baseScore = score
		}
	}

	// 频次系数
	var frequencyMultiplier float64 = 1.0
	if attackCount >= 100 {
		frequencyMultiplier = 1.8
	} else if attackCount >= 50 {
		frequencyMultiplier = 1.6
	} else if attackCount >= 20 {
		frequencyMultiplier = 1.4
	} else if attackCount >= 10 {
		frequencyMultiplier = 1.2
	} else if attackCount >= 5 {
		frequencyMultiplier = 1.1
	}

	// 计算最终评分
	finalScore := baseScore * frequencyMultiplier

	// 限制评分范围在1-10之间
	if finalScore > 10.0 {
		finalScore = 10.0
	} else if finalScore < 1.0 {
		finalScore = 1.0
	}

	return finalScore
}

// saveAttackSummariesToDB 将攻击统计数据保存到数据库
func saveAttackSummariesToDB(attackSummaries map[string]*AttackSummary) error {
	for _, summary := range attackSummaries {
		// 确定来源标签
		sourceLabel := ""
		if summary.DstLabel == "通信中心" || summary.DstLabel == "交通运输部" {
			sourceLabel = summary.DstLabel
		} else if summary.SrcLabel == "通信中心" || summary.SrcLabel == "交通运输部" {
			sourceLabel = summary.SrcLabel
		}

		// 获取主要攻击类别（出现次数最多的类别）
		var mainCategory string
		maxCount := 0
		for category, count := range summary.Categories {
			if count > maxCount {
				maxCount = count
				mainCategory = category
			}
		}

		// 计算威胁评分
		threatScore := calculateSimpleThreatScore(summary.Severities, summary.AttackCount)

		// 解析时间（如果需要可以在这里处理时间格式）
		// firstAttackTime, _ := time.Parse("2006-01-02 15:04:05", summary.FirstSeen)
		// lastAttackTime, _ := time.Parse("2006-01-02 15:04:05", summary.LastSeen)

		// 临时输出到控制台
		fmt.Printf("准备保存到数据库: 攻击IP=%s, 受害IP=%s, 来源标签=%s, 攻击类别=%s, 攻击次数=%d, 威胁评分=%.2f\n",
			summary.SrcIP, summary.DstIP, sourceLabel, mainCategory, summary.AttackCount, threatScore)
	}

	return nil
}

// getIOCIntelligenceDataFromDB 从数据库获取IOC情报数据
func getIOCIntelligenceDataFromDB(page, pageSize int) ([]IOCIntelligenceData, int64, error) {
	// 注意：这里需要数据库连接，实际使用时需要导入gorm和数据库驱动
	// 这是一个示例函数，展示如何查询数据

	var data []IOCIntelligenceData
	var total int64

	// 这里应该执行数据库查询操作
	// db.Model(&IOCIntelligenceData{}).Count(&total)
	// db.Order("threat_score DESC, attack_count DESC").
	//    Offset((page - 1) * pageSize).
	//    Limit(pageSize).
	//    Find(&data)

	// 临时返回空数据，实际使用时删除
	return data, total, nil
}

// printIOCWhitelist 打印当前IOC白名单
func printIOCWhitelist() {
	fmt.Printf("\n=== IOC白名单配置 ===\n")
	if len(iocWhitelist) == 0 {
		fmt.Printf("白名单为空\n")
		return
	}

	count := 1
	for ioc := range iocWhitelist {
		fmt.Printf("%d. %s\n", count, ioc)
		count++
	}
	fmt.Printf("总计: %d 个白名单IOC\n", len(iocWhitelist))
}

// getSourceLabel 根据IP地址获取来源标签
func getSourceLabel(ip string) string {
	if ip == "" {
		return "未知"
	}

	// 解析IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "未知"
	}

	// 检查IPv4地址
	if ipv4 := parsedIP.To4(); ipv4 != nil {
		// 检查10段 - 交通运输部
		if ipv4[0] == 10 {
			return "交通运输部"
		}

		// 检查192段 - 通信中心
		if ipv4[0] == 192 && ipv4[1] == 168 {
			return "通信中心"
		}

		// 检查172段 - 可以根据需要添加更多标签
		if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
			return "内网设备"
		}
	}

	// 外网地址
	if !IsPrivateIP(ip) {
		return "外网"
	}

	return "内网"
}

// AttackSummary 攻击统计结构
type AttackSummary struct {
	UUID         string               // 数据源唯一标识
	SrcIP        string
	DstIP        string
	DstIPs       []string             // 受害IP列表（用于外网IP攻击多个目标的情况）
	SrcLabel     string           // 攻击者来源标签
	DstLabel     string           // 受害者来源标签
	DstLabels    []string             // 受害单位标签列表
	AttackCount  int
	Categories   map[string]int    // 攻击类别及次数
	Severities   map[string]int    // 严重程度及次数
	IOCs         map[string]int    // IOC及次数
	FirstSeen    string           // 首次发现时间
	LastSeen     string           // 最后发现时间
	SampleRecord SearchDataRecord  // 样本记录
	UUIDs        []string             // 合并的UUID列表
}

// IOCWhitelist IOC白名单管理
type IOCWhitelist struct {
	IPs     map[string]bool
	Domains map[string]bool
}

// NewIOCWhitelist 创建新的IOC白名单
func NewIOCWhitelist() *IOCWhitelist {
	return &IOCWhitelist{
		IPs:     make(map[string]bool),
		Domains: make(map[string]bool),
	}
}

// AddIP 添加IP到白名单
func (w *IOCWhitelist) AddIP(ip string) {
	w.IPs[ip] = true
}

// AddDomain 添加域名到白名单
func (w *IOCWhitelist) AddDomain(domain string) {
	w.Domains[domain] = true
}

// IsWhitelisted 检查IOC是否在白名单中
func (w *IOCWhitelist) IsWhitelisted(ioc string) bool {
	// 检查是否为IP地址
	if net.ParseIP(ioc) != nil {
		return w.IPs[ioc]
	}

	// 检查是否为域名
	return w.Domains[ioc]
}

// IsRecordWhitelisted 检查记录是否命中白名单
func (w *IOCWhitelist) IsRecordWhitelisted(record SearchDataRecord) bool {
	// 检查源IP
	if w.IsWhitelisted(record.SrcIP) {
		return true
	}

	// 检查目标IP
	if w.IsWhitelisted(record.DstIP) {
		return true
	}

	// 检查IOC字段
	if record.IOC != "" && w.IsWhitelisted(record.IOC) {
		return true
	}

	// 检查查询域名（DNS查询）
	if record.Query != "" && w.IsWhitelisted(record.Query) {
		return true
	}

	// 检查主机名
	if record.Host != "" && w.IsWhitelisted(record.Host) {
		return true
	}

	// 检查服务器名
	if record.ServerName != "" && w.IsWhitelisted(record.ServerName) {
		return true
	}

	return false
}

// LoadDefaultWhitelist 加载默认白名单（示例）
func (w *IOCWhitelist) LoadDefaultWhitelist() {
	// 添加常见的内部服务IP
	w.AddIP("127.0.0.1")
	w.AddIP("::1")

	// 添加常见的合法域名
	w.AddDomain("microsoft.com")
	w.AddDomain("windows.com")
	w.AddDomain("office.com")
	w.AddDomain("live.com")
	w.AddDomain("msftconnecttest.com")
	w.AddDomain("google.com")
	w.AddDomain("googleapis.com")
	w.AddDomain("gstatic.com")
	w.AddDomain("doubleclick.net")
	w.AddDomain("googlesyndication.com")
	w.AddDomain("baidu.com")
	w.AddDomain("qq.com")
	w.AddDomain("tencent.com")
	w.AddDomain("aliyun.com")
	w.AddDomain("alibaba.com")
	w.AddDomain("taobao.com")
	w.AddDomain("tmall.com")

	fmt.Printf("已加载默认白名单：%d个IP，%d个域名\n", len(w.IPs), len(w.Domains))
}

// SearchAdvancedDataPage 调用高级搜索数据API（单页）
func SearchAdvancedDataPage(host, userKey string, startTime, endTime int64, page, size int) (*SearchDataResponse, error) {
	client := resty.New()

	// 跳过SSL证书验证（用于内网环境）
	client.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})

	// 构建请求数据
	requestData := SearchDataRequest{
		Severity:         "",
		Category:         "",
		SrcIP:           "",
		DstIP:           "",
		EmailFrom:       "",
		To:              "",
		AttackStatus:    "",
		AppProto:        "",
		Method:          "",
		StatusCode:      "",
		MD5:             "",
		XFF:             "",
		Domain:          "",
		Username:        "",
		VisitDirection:  "",
		IOC:             "",
		AppendQuery:     "",
		Start:           startTime,
		End:             endTime,
		QuickQuery:      "",
		FilterQuery:     "",
		IsCollectHistory: false,
		QueryFields: []string{
			"xff", "src_ip_country", "src_ip_city", "attacker", "victim",
			"src_ip_hostName", "src_ip_isHost", "host", "server_name",
			"dst_ip_country", "dst_ip_city", "dst_ip_hostName", "dst_ip_isHost",
			"dst_port", "method", "uri", "status_code", "user_agent", "query",
			"answers", "qtype_name", "email_from", "to", "attachment_names",
			"md5", "platform", "username", "password", "timestamp", "src_ip",
			"dst_ip", "app_proto", "category", "severity", "ioc", "attack_status",
			"desc", "rid",
		},
		From:          page,
		Size:          size,
		IsDeal:        "",
		Sort:          "",
		Order:         "",
		PageQueryMode: "DATA",
		Groups:        []string{"uuid"},
		TableName:     "event_data",
	}

	// 发送POST请求
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("user-key", userKey).
		SetBody(requestData).
		SetResult(&SearchDataResponse{}).
		Post(fmt.Sprintf("https://%s/workbenchApi/furious/searchCenter/advanced/searchData", host))

	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	result, ok := resp.Result().(*SearchDataResponse)
	if !ok {
		return nil, fmt.Errorf("响应解析失败")
	}

	return result, nil
}

// SearchAdvancedDataAll 获取指定时间范围内的所有数据（自动翻页）
func SearchAdvancedDataAll(host, userKey string, startTime, endTime int64) ([]SearchDataRecord, error) {
	var allRecords []SearchDataRecord
	page := 1
	size := 100 // 每页获取100条记录

	fmt.Printf("开始获取所有数据，每页 %d 条记录...\n", size)

	for {
		fmt.Printf("正在获取第 %d 页数据...\n", page)

		result, err := SearchAdvancedDataPage(host, userKey, startTime, endTime, page, size)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页数据失败: %v", page, err)
		}

		// 添加当前页的记录
		allRecords = append(allRecords, result.Records...)

		fmt.Printf("第 %d 页获取到 %d 条记录，累计 %d 条\n", page, len(result.Records), len(allRecords))

		// 检查是否还有更多数据
		if len(result.Records) < size || len(allRecords) >= result.Total {
			break
		}

		page++
	}

	fmt.Printf("数据获取完成，总计 %d 条记录\n", len(allRecords))
	return allRecords, nil
}

// mergeAttackData 合并攻击数据，统计相同源IP和目标IP的攻击次数（支持白名单过滤）
func mergeAttackData(records []SearchDataRecord) (map[string]*AttackSummary, int, int, int) {
	attackMap := make(map[string]*AttackSummary)
	internalCount := 0
	whitelistCount := 0
	filteredCount := 0

	for _, record := range records {
		// 统计内网流量
		if isInternalTraffic(record) {
			internalCount++
			continue
		}

		// 统计白名单IOC
		if isIOCWhitelisted(record.IOC) {
			whitelistCount++
			continue
		}

		// 检查是否涉及目标组织（通信中心或交通运输部）
		if !shouldIncludeAttackFlow(record.SrcIP, record.DstIP) {
			filteredCount++
			continue
		}

		// 生成唯一键：源IP -> 目标IP
		key := fmt.Sprintf("%s->%s", record.SrcIP, record.DstIP)

		if summary, exists := attackMap[key]; exists {
			// 更新现有记录
			summary.AttackCount++

			// 更新类别统计
			if record.Category != "" {
				summary.Categories[record.Category]++
			}

			// 更新严重程度统计
			if record.Severity != "" {
				summary.Severities[record.Severity]++
			}

			// 更新IOC统计
			if record.IOC != "" {
				summary.IOCs[record.IOC]++
			}

			// 更新时间范围
			if record.TimestampCN < summary.FirstSeen {
				summary.FirstSeen = record.TimestampCN
			}
			if record.TimestampCN > summary.LastSeen {
				summary.LastSeen = record.TimestampCN
			}
		} else {
			// 创建新记录
			dstLabel := getSourceLabel(record.DstIP)
			summary := &AttackSummary{
				UUID:        record.UUID,
				SrcIP:       record.SrcIP,
				DstIP:       record.DstIP,
				DstIPs:      []string{record.DstIP},
				SrcLabel:    getSourceLabel(record.SrcIP),
				DstLabel:    dstLabel,
				DstLabels:   []string{dstLabel},
				AttackCount: 1,
				Categories:  make(map[string]int),
				Severities:  make(map[string]int),
				IOCs:        make(map[string]int),
				FirstSeen:   record.TimestampCN,
				LastSeen:    record.TimestampCN,
				SampleRecord: record,
				UUIDs:       []string{record.UUID},
			}

			// 初始化统计
			if record.Category != "" {
				summary.Categories[record.Category] = 1
			}
			if record.Severity != "" {
				summary.Severities[record.Severity] = 1
			}
			if record.IOC != "" {
				summary.IOCs[record.IOC] = 1
			}

			attackMap[key] = summary
		}
	}

	return attackMap, internalCount, whitelistCount, filteredCount
}

// 测试函数（原main函数）
func TestCCCCBlackTechnology() {
	// 测试参数
	host := "**********:5443"
	userKey := "dbb5f1c43267472a8b771b2373407e8b"

	// 设置时间范围（示例：最近1小时）
	endTime := time.Now().UnixMilli()
	startTime := endTime - 3600*1000 // 1小时前

	fmt.Printf("开始测试API调用...\n")
	fmt.Printf("主机: %s\n", host)
	fmt.Printf("时间范围: %s 到 %s\n",
		time.Unix(startTime/1000, 0).Format("2006-01-02 15:04:05"),
		time.Unix(endTime/1000, 0).Format("2006-01-02 15:04:05"))

	// 显示当前白名单配置
	printIOCWhitelist()

	// 获取所有数据
	allRecords, err := SearchAdvancedDataAll(host, userKey, startTime, endTime)
	if err != nil {
		fmt.Printf("API调用失败: %v\n", err)
		return
	}

	// 合并攻击数据
	fmt.Printf("\n=== 开始数据分析 ===\n")
	attackSummaries, internalCount, whitelistCount, filteredCount := mergeAttackData(allRecords)

	// 输出结果统计
	fmt.Printf("\n=== 数据统计 ===\n")
	fmt.Printf("原始记录总数: %d\n", len(allRecords))
	fmt.Printf("内网流量记录数: %d (已过滤)\n", internalCount)
	fmt.Printf("白名单IOC记录数: %d (已过滤)\n", whitelistCount)
	fmt.Printf("非目标组织记录数: %d (已过滤)\n", filteredCount)
	fmt.Printf("目标组织威胁记录数: %d\n", len(allRecords)-internalCount-whitelistCount-filteredCount)
	fmt.Printf("合并后攻击流数: %d\n", len(attackSummaries))

	// 输出合并后的攻击统计
	if len(attackSummaries) > 0 {
		fmt.Printf("\n=== 攻击流统计（仅显示涉及通信中心/交通运输部的威胁，按攻击次数排序）===\n")

		// 转换为切片并按攻击次数排序
		type sortableAttack struct {
			key     string
			summary *AttackSummary
		}

		var attacks []sortableAttack
		for key, summary := range attackSummaries {
			attacks = append(attacks, sortableAttack{key: key, summary: summary})
		}

		// 简单的冒泡排序（按攻击次数降序）
		for i := 0; i < len(attacks)-1; i++ {
			for j := 0; j < len(attacks)-1-i; j++ {
				if attacks[j].summary.AttackCount < attacks[j+1].summary.AttackCount {
					attacks[j], attacks[j+1] = attacks[j+1], attacks[j]
				}
			}
		}

		// 输出前20个最活跃的攻击流
		maxDisplay := 20
		if len(attacks) < maxDisplay {
			maxDisplay = len(attacks)
		}

		for i := 0; i < maxDisplay; i++ {
			attack := attacks[i]
			summary := attack.summary

			fmt.Printf("攻击流 %d:\n", i+1)
			fmt.Printf("  攻击者: %s (%s)\n", summary.SrcIP,
				func() string {
					if IsPrivateIP(summary.SrcIP) {
						return "内网"
					}
					return "外网"
				}())
			fmt.Printf("  受害者: %s (%s)\n", summary.DstIP,
				func() string {
					if IsPrivateIP(summary.DstIP) {
						return "内网"
					}
					return "外网"
				}())
			// 只显示目标组织的标签
			if summary.DstLabel == "通信中心" || summary.DstLabel == "交通运输部" {
				fmt.Printf("  来源标签: %s\n", summary.DstLabel)
			} else if summary.SrcLabel == "通信中心" || summary.SrcLabel == "交通运输部" {
				fmt.Printf("  来源标签: %s\n", summary.SrcLabel)
			}
			fmt.Printf("  攻击次数: %d\n", summary.AttackCount)
			fmt.Printf("  时间范围: %s ~ %s\n", summary.FirstSeen, summary.LastSeen)

			// 输出攻击类别统计
			if len(summary.Categories) > 0 {
				fmt.Printf("  攻击类别: ")
				first := true
				for category, count := range summary.Categories {
					if !first {
						fmt.Printf(", ")
					}
					fmt.Printf("%s(%d次)", category, count)
					first = false
				}
				fmt.Printf("\n")
			}

			// 输出严重程度统计
			if len(summary.Severities) > 0 {
				fmt.Printf("  严重程度: ")
				first := true
				for severity, count := range summary.Severities {
					if !first {
						fmt.Printf(", ")
					}
					fmt.Printf("%s(%d次)", severity, count)
					first = false
				}
				fmt.Printf("\n")
			}

			// 输出主要IOC
			if len(summary.IOCs) > 0 {
				fmt.Printf("  主要IOC: ")
				first := true
				count := 0
				for ioc, iocCount := range summary.IOCs {
					if count >= 3 { // 只显示前3个IOC
						break
					}
					if !first {
						fmt.Printf(", ")
					}
					fmt.Printf("%s(%d次)", ioc, iocCount)
					first = false
					count++
				}
				fmt.Printf("\n")
			}

			fmt.Printf("  ---\n")
		}

		if len(attacks) > maxDisplay {
			fmt.Printf("... 还有 %d 个攻击流未显示\n", len(attacks)-maxDisplay)
		}

		// 保存数据到数据库
		fmt.Printf("\n=== 保存数据到数据库 ===\n")
		err = saveAttackSummariesToDB(attackSummaries)
		if err != nil {
			fmt.Printf("保存数据到数据库失败: %v\n", err)
		} else {
			fmt.Printf("数据已成功保存到数据库\n")
		}
	} else {
		fmt.Printf("\n没有找到涉及通信中心或交通运输部的威胁记录\n")
	}

	fmt.Printf("\n测试完成！\n")
}

