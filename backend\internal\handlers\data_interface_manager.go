package handlers

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
	"vulnerability_push/internal/models"
)

// DataInterfaceManager 数据接口管理器
// 统一管理数据接口的生命周期和执行
type DataInterfaceManager struct {
	db       *gorm.DB
	registry *DataInterfaceRegistry
	factory  *DataInterfaceFactory
	handler  *DataInterfaceHandler
	
	// 执行状态管理
	runningInterfaces map[uint]bool
	mutex            sync.RWMutex
}

// NewDataInterfaceManager 创建数据接口管理器
func NewDataInterfaceManager(handler *DataInterfaceHandler) *DataInterfaceManager {
	manager := &DataInterfaceManager{
		db:                handler.db,
		registry:          handler.registry,
		handler:          handler,
		runningInterfaces: make(map[uint]bool),
	}
	
	manager.factory = NewDataInterfaceFactory(handler)
	
	return manager
}

// IsInterfaceRunning 检查接口是否正在运行
func (m *DataInterfaceManager) IsInterfaceRunning(interfaceID uint) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	return m.runningInterfaces[interfaceID]
}

// SetInterfaceRunning 设置接口运行状态
func (m *DataInterfaceManager) SetInterfaceRunning(interfaceID uint, running bool) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if running {
		m.runningInterfaces[interfaceID] = true
	} else {
		delete(m.runningInterfaces, interfaceID)
	}
}

// ExecuteInterface 执行数据接口
func (m *DataInterfaceManager) ExecuteInterface(dataInterface *models.DataInterface) error {
	// 检查接口是否已在运行
	if m.IsInterfaceRunning(dataInterface.ID) {
		return fmt.Errorf("接口 %s 正在运行中", dataInterface.Name)
	}
	
	// 标记为运行中
	m.SetInterfaceRunning(dataInterface.ID, true)
	defer m.SetInterfaceRunning(dataInterface.ID, false)
	
	// 记录执行开始
	fmt.Printf("开始执行数据接口: %s (ID: %d)\n", dataInterface.Name, dataInterface.ID)
	
	// 创建执行日志
	log := &models.DataInterfaceLog{
		InterfaceID: dataInterface.ID,
		Message:     "开始执行数据接口采集",
	}
	log.MarkAsStarted()
	
	// 保存开始日志
	if err := m.db.Create(log).Error; err != nil {
		return fmt.Errorf("创建执行日志失败: %v", err)
	}
	
	// 获取接口执行器
	executor, exists := m.registry.Get(dataInterface.Type)
	if !exists {
		m.updateExecutionLog(log, "failed", fmt.Sprintf("不支持的接口类型: %s", dataInterface.Type))
		m.updateDataInterfaceStats(dataInterface, "failed", fmt.Sprintf("不支持的接口类型: %s", dataInterface.Type), 0)
		return fmt.Errorf("不支持的接口类型: %s", dataInterface.Type)
	}
	
	// 解析配置
	var config map[string]interface{}
	if dataInterface.Config != "" {
		if err := json.Unmarshal([]byte(dataInterface.Config), &config); err != nil {
			m.updateExecutionLog(log, "failed", fmt.Sprintf("配置解析失败: %v", err))
			m.updateDataInterfaceStats(dataInterface, "failed", fmt.Sprintf("配置解析失败: %v", err), 0)
			return fmt.Errorf("配置解析失败: %v", err)
		}
	}
	
	// 执行接口采集
	if err := executor.Execute(dataInterface, config, log); err != nil {
		m.updateExecutionLog(log, "failed", fmt.Sprintf("接口执行失败: %v", err))
		m.updateDataInterfaceStats(dataInterface, "failed", fmt.Sprintf("接口执行失败: %v", err), 0)
		return fmt.Errorf("接口执行失败: %v", err)
	}
	
	// 更新最后执行时间
	dataInterface.LastRunAt = time.Now().Unix()
	m.db.Save(dataInterface)
	
	fmt.Printf("数据接口执行完成: %s (ID: %d)\n", dataInterface.Name, dataInterface.ID)
	return nil
}

// ValidateInterfaceConfig 验证接口配置
func (m *DataInterfaceManager) ValidateInterfaceConfig(interfaceType string, config map[string]interface{}) error {
	executor, exists := m.registry.Get(interfaceType)
	if !exists {
		return fmt.Errorf("不支持的接口类型: %s", interfaceType)
	}
	
	return executor.ValidateConfig(config)
}

// GetInterfaceConfigSchema 获取接口配置模式
func (m *DataInterfaceManager) GetInterfaceConfigSchema(interfaceType string) (map[string]interface{}, error) {
	executor, exists := m.registry.Get(interfaceType)
	if !exists {
		return nil, fmt.Errorf("不支持的接口类型: %s", interfaceType)
	}
	
	return executor.GetConfigSchema(), nil
}

// GetRunningInterfaces 获取正在运行的接口列表
func (m *DataInterfaceManager) GetRunningInterfaces() []uint {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var running []uint
	for interfaceID := range m.runningInterfaces {
		running = append(running, interfaceID)
	}
	
	return running
}

// StopInterface 停止接口执行（如果支持）
func (m *DataInterfaceManager) StopInterface(interfaceID uint) error {
	// 目前简单实现为标记为非运行状态
	// 实际实现可能需要更复杂的停止逻辑
	m.SetInterfaceRunning(interfaceID, false)
	return nil
}

// GetInterfaceStats 获取接口统计信息
func (m *DataInterfaceManager) GetInterfaceStats(interfaceID uint) (map[string]interface{}, error) {
	var dataInterface models.DataInterface
	if err := m.db.First(&dataInterface, interfaceID).Error; err != nil {
		return nil, err
	}
	
	// 获取执行日志统计
	var totalRuns, successRuns, failedRuns int64
	m.db.Model(&models.DataInterfaceLog{}).Where("interface_id = ?", interfaceID).Count(&totalRuns)
	m.db.Model(&models.DataInterfaceLog{}).Where("interface_id = ? AND status = ?", interfaceID, "success").Count(&successRuns)
	m.db.Model(&models.DataInterfaceLog{}).Where("interface_id = ? AND status = ?", interfaceID, "failed").Count(&failedRuns)
	
	// 计算成功率
	var successRate float64
	if totalRuns > 0 {
		successRate = float64(successRuns) / float64(totalRuns) * 100
	}
	
	// 获取最近执行时间
	var lastLog models.DataInterfaceLog
	m.db.Where("interface_id = ?", interfaceID).Order("created_at DESC").First(&lastLog)
	
	return map[string]interface{}{
		"totalRuns":    totalRuns,
		"successRuns":  successRuns,
		"failedRuns":   failedRuns,
		"successRate":  successRate,
		"lastRunTime":  lastLog.CreatedAt,
		"lastStatus":   lastLog.Status,
		"isRunning":    m.IsInterfaceRunning(interfaceID),
	}, nil
}

// updateExecutionLog 更新执行日志
func (m *DataInterfaceManager) updateExecutionLog(log *models.DataInterfaceLog, status, message string) {
	log.MarkAsCompleted(status, message, 0)
	m.db.Save(log)
}

// updateDataInterfaceStats 更新数据接口统计
func (m *DataInterfaceManager) updateDataInterfaceStats(dataInterface *models.DataInterface, status, message string, dataCount int) {
	// 这里可以实现更详细的统计更新逻辑
	fmt.Printf("接口 %s 执行结果: %s, 消息: %s, 数据量: %d\n", 
		dataInterface.Name, status, message, dataCount)
}
