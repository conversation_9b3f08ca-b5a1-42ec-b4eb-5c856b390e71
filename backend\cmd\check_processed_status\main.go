package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查IOC源数据表的处理状态分布
	fmt.Println("\n📊 检查IOC源数据表的处理状态分布:")
	
	type StatusCount struct {
		ProcessedStatus string `json:"processed_status"`
		Count          int64  `json:"count"`
	}
	
	var statusCounts []StatusCount
	err = db.Table("ioc_source_data").
		Select("processed_status, COUNT(*) as count").
		Group("processed_status").
		Find(&statusCounts).Error
	
	if err != nil {
		log.Fatalf("查询处理状态分布失败: %v", err)
	}
	
	for _, sc := range statusCounts {
		fmt.Printf("  %s: %d 条\n", sc.ProcessedStatus, sc.Count)
	}
	
	// 检查满足条件的数据
	fmt.Println("\n🔍 检查满足条件的数据:")
	
	// 检查攻击次数 >= 3 的数据
	var attackCountGte3 int64
	db.Table("ioc_source_data").Where("attack_count >= ?", 3).Count(&attackCountGte3)
	fmt.Printf("  攻击次数 >= 3: %d 条\n", attackCountGte3)
	
	// 检查威胁评分 >= 2 的数据
	var threatScoreGte2 int64
	db.Table("ioc_source_data").Where("threat_score >= ?", 2).Count(&threatScoreGte2)
	fmt.Printf("  威胁评分 >= 2: %d 条\n", threatScoreGte2)
	
	// 检查同时满足两个条件的数据
	var bothConditions int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ?", 3, 2).
		Count(&bothConditions)
	fmt.Printf("  同时满足两个条件: %d 条\n", bothConditions)
	
	// 检查同时满足两个条件且未处理的数据
	var bothConditionsUnprocessed int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&bothConditionsUnprocessed)
	fmt.Printf("  同时满足两个条件且未处理: %d 条\n", bothConditionsUnprocessed)
	
	// 显示一些具体的数据示例
	fmt.Println("\n📋 显示一些具体的数据示例:")
	
	type SampleData struct {
		ID              uint    `json:"id"`
		AttackIP        string  `json:"attack_ip"`
		VictimIP        string  `json:"victim_ip"`
		AttackCount     int     `json:"attack_count"`
		ThreatScore     float64 `json:"threat_score"`
		ProcessedStatus string  `json:"processed_status"`
		Category        string  `json:"category"`
	}
	
	var samples []SampleData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Where("attack_count >= ? AND threat_score >= ?", 3, 2).
		Limit(5).
		Find(&samples)
	
	for _, sample := range samples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
}
