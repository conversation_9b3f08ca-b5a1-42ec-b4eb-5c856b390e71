package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// BaseHandler 基础处理器
type BaseHandler struct {
	// 这里可以注入通用的依赖，如日志记录器、服务管理器等
}

// NewBaseHandler 创建基础处理器
func NewBaseHandler() *BaseHandler {
	return &BaseHandler{}
}

// Response 标准响应结构
type Response struct {
	Code      int         `json:"code"`
	Message   string      `json:"msg"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"requestId,omitempty"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	PageSize    int   `json:"pageSize"`
	TotalPages  int   `json:"totalPages"`
	HasNext     bool  `json:"hasNext"`
	HasPrevious bool  `json:"hasPrevious"`
}

// ListResponse 列表响应结构
type ListResponse struct {
	List       interface{}        `json:"list"`
	Pagination PaginationResponse `json:"pagination"`
}

// 响应辅助方法

// Success 成功响应
func (h *BaseHandler) Success(c *gin.Context, data interface{}) {
	h.JSON(c, http.StatusOK, 200, "操作成功", data)
}

// SuccessWithMessage 带消息的成功响应
func (h *BaseHandler) SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	h.JSON(c, http.StatusOK, 200, message, data)
}

// Error 错误响应
func (h *BaseHandler) Error(c *gin.Context, httpCode, code int, message string) {
	h.JSON(c, httpCode, code, message, nil)
}

// BadRequest 400错误响应
func (h *BaseHandler) BadRequest(c *gin.Context, message string) {
	h.Error(c, http.StatusBadRequest, 400, message)
}

// Unauthorized 401错误响应
func (h *BaseHandler) Unauthorized(c *gin.Context, message string) {
	h.Error(c, http.StatusUnauthorized, 401, message)
}

// Forbidden 403错误响应
func (h *BaseHandler) Forbidden(c *gin.Context, message string) {
	h.Error(c, http.StatusForbidden, 403, message)
}

// NotFound 404错误响应
func (h *BaseHandler) NotFound(c *gin.Context, message string) {
	h.Error(c, http.StatusNotFound, 404, message)
}

// InternalServerError 500错误响应
func (h *BaseHandler) InternalServerError(c *gin.Context, message string) {
	h.Error(c, http.StatusInternalServerError, 500, message)
}

// ValidationError 验证错误响应
func (h *BaseHandler) ValidationError(c *gin.Context, err error) {
	h.BadRequest(c, "参数验证失败: "+err.Error())
}

// JSON 通用JSON响应
func (h *BaseHandler) JSON(c *gin.Context, httpCode, code int, message string, data interface{}) {
	requestID, _ := c.Get("RequestID")
	requestIDStr, _ := requestID.(string)

	response := Response{
		Code:      code,
		Message:   message,
		Data:      data,
		Timestamp: getCurrentTimestamp(),
		RequestID: requestIDStr,
	}

	c.JSON(httpCode, response)
}

// PaginatedSuccess 分页成功响应
func (h *BaseHandler) PaginatedSuccess(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	listResponse := ListResponse{
		List: data,
		Pagination: PaginationResponse{
			Total:       total,
			Page:        page,
			PageSize:    pageSize,
			TotalPages:  totalPages,
			HasNext:     page < totalPages,
			HasPrevious: page > 1,
		},
	}

	h.Success(c, listResponse)
}

// 参数获取辅助方法

// GetIDParam 从URL参数获取ID
func (h *BaseHandler) GetIDParam(c *gin.Context, paramName string) (uint, error) {
	idStr := c.Param(paramName)
	if idStr == "" {
		return 0, NewValidationError(paramName, "ID不能为空")
	}
	
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, NewValidationError(paramName, "ID格式不正确")
	}
	
	if id == 0 {
		return 0, NewValidationError(paramName, "ID不能为0")
	}
	
	return uint(id), nil
}

// GetQueryParam 获取查询参数
func (h *BaseHandler) GetQueryParam(c *gin.Context, key, defaultValue string) string {
	value := c.Query(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetQueryInt 获取整数查询参数
func (h *BaseHandler) GetQueryInt(c *gin.Context, key string, defaultValue int) int {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// GetQueryBool 获取布尔查询参数
func (h *BaseHandler) GetQueryBool(c *gin.Context, key string, defaultValue bool) bool {
	valueStr := c.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	
	value, err := strconv.ParseBool(valueStr)
	if err != nil {
		return defaultValue
	}
	
	return value
}

// GetPaginationParams 获取分页参数
func (h *BaseHandler) GetPaginationParams(c *gin.Context) (page, pageSize int) {
	page = h.GetQueryInt(c, "page", 1)
	pageSize = h.GetQueryInt(c, "pageSize", 20)
	
	// 验证分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 5 {
		pageSize = 20
	} else if pageSize > 100 {
		pageSize = 100
	}
	
	return page, pageSize
}

// GetSortParams 获取排序参数
func (h *BaseHandler) GetSortParams(c *gin.Context, defaultSortBy, defaultSortOrder string) (sortBy, sortOrder string) {
	sortBy = h.GetQueryParam(c, "sortBy", defaultSortBy)
	sortOrder = h.GetQueryParam(c, "sortOrder", defaultSortOrder)
	
	// 验证排序方向
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = defaultSortOrder
	}
	
	return sortBy, sortOrder
}

// GetDateRangeParams 获取日期范围参数
func (h *BaseHandler) GetDateRangeParams(c *gin.Context) (startDate, endDate string) {
	startDate = h.GetQueryParam(c, "startDate", "")
	endDate = h.GetQueryParam(c, "endDate", "")
	
	return startDate, endDate
}

// BindJSON 绑定JSON数据
func (h *BaseHandler) BindJSON(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindJSON(obj); err != nil {
		return NewValidationError("body", "JSON数据格式错误: "+err.Error())
	}
	return nil
}

// BindQuery 绑定查询参数
func (h *BaseHandler) BindQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return NewValidationError("query", "查询参数格式错误: "+err.Error())
	}
	return nil
}

// 用户信息获取方法

// GetCurrentUser 获取当前用户信息
func (h *BaseHandler) GetCurrentUser(c *gin.Context) (userID uint, username, role string, exists bool) {
	userIDVal, userIDExists := c.Get("user_id")
	usernameVal, usernameExists := c.Get("username")
	roleVal, roleExists := c.Get("user_role")

	if !userIDExists || !usernameExists || !roleExists {
		return 0, "", "", false
	}

	userID, ok1 := userIDVal.(uint)
	username, ok2 := usernameVal.(string)
	role, ok3 := roleVal.(string)

	if !ok1 || !ok2 || !ok3 {
		return 0, "", "", false
	}

	return userID, username, role, true
}

// GetCurrentUserID 获取当前用户ID
func (h *BaseHandler) GetCurrentUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}

	id, ok := userID.(uint)
	return id, ok
}

// IsAdmin 检查当前用户是否为管理员
func (h *BaseHandler) IsAdmin(c *gin.Context) bool {
	role, exists := c.Get("user_role")
	if !exists {
		return false
	}

	roleStr, ok := role.(string)
	return ok && roleStr == "admin"
}

// RequireAdmin 要求管理员权限
func (h *BaseHandler) RequireAdmin(c *gin.Context) bool {
	if !h.IsAdmin(c) {
		h.Forbidden(c, "需要管理员权限")
		return false
	}
	return true
}

// RequireAuth 要求用户认证
func (h *BaseHandler) RequireAuth(c *gin.Context) bool {
	if _, exists := h.GetCurrentUserID(c); !exists {
		h.Unauthorized(c, "需要用户认证")
		return false
	}
	return true
}

// 错误类型定义

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Error 实现error接口
func (ve ValidationError) Error() string {
	return ve.Message
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string) ValidationError {
	return ValidationError{
		Field:   field,
		Message: message,
	}
}

// 工具函数

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}
