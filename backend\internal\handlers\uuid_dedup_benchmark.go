package handlers

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
	"vulnerability_push/internal/models"
	"gorm.io/gorm"
)

// UUIDDedupBenchmark UUID去重性能测试工具
type UUIDDedupBenchmark struct {
	db   *gorm.DB
	base *BaseDataInterface
}

// NewUUIDDedupBenchmark 创建性能测试工具
func NewUUIDDedupBenchmark(db *gorm.DB) *UUIDDedupBenchmark {
	handler := &DataInterfaceHandler{db: db}
	base := NewBaseDataInterface(handler)
	
	return &UUIDDedupBenchmark{
		db:   db,
		base: base,
	}
}

// BenchmarkResult 性能测试结果
type BenchmarkResult struct {
	Method        string        `json:"method"`
	DataSize      int           `json:"data_size"`
	Duration      time.Duration `json:"duration"`
	ThroughputQPS float64       `json:"throughput_qps"`
	BatchSize     int           `json:"batch_size"`
	WorkerCount   int           `json:"worker_count"`
	Error         string        `json:"error,omitempty"`
}

// generateTestUUIDs 生成测试用的UUID列表
func (b *UUIDDedupBenchmark) generateTestUUIDs(count int, existingRatio float64) ([]string, error) {
	var uuids []string
	
	// 生成一些已存在的UUID（用于测试去重效果）
	existingCount := int(float64(count) * existingRatio)
	if existingCount > 0 {
		var existingUUIDs []models.ProcessedUUID
		for i := 0; i < existingCount; i++ {
			uuid := fmt.Sprintf("test-uuid-%d-%d", time.Now().Unix(), i)
			existingUUIDs = append(existingUUIDs, models.ProcessedUUID{
				UUID:        uuid,
				SourceType:  "benchmark_test",
				ProcessedAt: time.Now(),
			})
			uuids = append(uuids, uuid)
		}
		
		// 批量插入已存在的UUID
		if err := b.db.CreateInBatches(existingUUIDs, 1000).Error; err != nil {
			return nil, fmt.Errorf("创建测试UUID失败: %v", err)
		}
	}
	
	// 生成新的UUID
	newCount := count - existingCount
	for i := 0; i < newCount; i++ {
		uuid := fmt.Sprintf("new-uuid-%d-%d", time.Now().Unix(), rand.Intn(1000000))
		uuids = append(uuids, uuid)
	}
	
	// 打乱顺序
	rand.Shuffle(len(uuids), func(i, j int) {
		uuids[i], uuids[j] = uuids[j], uuids[i]
	})
	
	return uuids, nil
}

// benchmarkSingleCheck 测试单个查询性能
func (b *UUIDDedupBenchmark) benchmarkSingleCheck(uuids []string) *BenchmarkResult {
	start := time.Now()
	
	processedCount := 0
	for _, uuid := range uuids {
		if b.base.IsUUIDProcessed(uuid, "benchmark_test") {
			processedCount++
		}
	}
	
	duration := time.Since(start)
	throughput := float64(len(uuids)) / duration.Seconds()
	
	return &BenchmarkResult{
		Method:        "single",
		DataSize:      len(uuids),
		Duration:      duration,
		ThroughputQPS: throughput,
		BatchSize:     1,
		WorkerCount:   1,
	}
}

// benchmarkBatchCheck 测试批量查询性能
func (b *UUIDDedupBenchmark) benchmarkBatchCheck(uuids []string) *BenchmarkResult {
	start := time.Now()
	
	result, err := b.base.BatchCheckUUIDsProcessed(uuids, "benchmark_test")
	
	duration := time.Since(start)
	throughput := float64(len(uuids)) / duration.Seconds()
	
	benchResult := &BenchmarkResult{
		Method:        "batch",
		DataSize:      len(uuids),
		Duration:      duration,
		ThroughputQPS: throughput,
		BatchSize:     b.base.dedupConfig.BatchSize,
		WorkerCount:   1,
	}
	
	if err != nil {
		benchResult.Error = err.Error()
	} else {
		processedCount := 0
		for _, processed := range result {
			if processed {
				processedCount++
			}
		}
		fmt.Printf("批量查询结果: 总数=%d, 已处理=%d\n", len(uuids), processedCount)
	}
	
	return benchResult
}

// benchmarkConcurrentCheck 测试并发查询性能
func (b *UUIDDedupBenchmark) benchmarkConcurrentCheck(uuids []string) *BenchmarkResult {
	start := time.Now()
	
	result, err := b.base.ConcurrentBatchCheckUUIDsProcessed(uuids, "benchmark_test", 0, 0)
	
	duration := time.Since(start)
	throughput := float64(len(uuids)) / duration.Seconds()
	
	benchResult := &BenchmarkResult{
		Method:        "concurrent",
		DataSize:      len(uuids),
		Duration:      duration,
		ThroughputQPS: throughput,
		BatchSize:     b.base.dedupConfig.BatchSize,
		WorkerCount:   b.base.dedupConfig.WorkerCount,
	}
	
	if err != nil {
		benchResult.Error = err.Error()
	} else {
		processedCount := 0
		for _, processed := range result {
			if processed {
				processedCount++
			}
		}
		fmt.Printf("并发查询结果: 总数=%d, 已处理=%d\n", len(uuids), processedCount)
	}
	
	return benchResult
}

// RunBenchmark 运行完整的性能测试
func (b *UUIDDedupBenchmark) RunBenchmark(dataSize int, existingRatio float64) ([]*BenchmarkResult, error) {
	fmt.Printf("开始UUID去重性能测试 - 数据量: %d, 已存在比例: %.2f\n", dataSize, existingRatio)
	
	// 生成测试数据
	uuids, err := b.generateTestUUIDs(dataSize, existingRatio)
	if err != nil {
		return nil, fmt.Errorf("生成测试数据失败: %v", err)
	}
	
	var results []*BenchmarkResult
	
	// 测试单个查询性能（仅对小数据量测试）
	if dataSize <= 1000 {
		fmt.Println("测试单个查询性能...")
		results = append(results, b.benchmarkSingleCheck(uuids))
	}
	
	// 测试批量查询性能
	fmt.Println("测试批量查询性能...")
	results = append(results, b.benchmarkBatchCheck(uuids))
	
	// 测试并发查询性能
	fmt.Println("测试并发查询性能...")
	results = append(results, b.benchmarkConcurrentCheck(uuids))
	
	// 清理测试数据
	b.cleanupTestData()
	
	return results, nil
}

// cleanupTestData 清理测试数据
func (b *UUIDDedupBenchmark) cleanupTestData() {
	b.db.Where("source_type = ?", "benchmark_test").Delete(&models.ProcessedUUID{})
}

// PrintBenchmarkResults 打印性能测试结果
func (b *UUIDDedupBenchmark) PrintBenchmarkResults(results []*BenchmarkResult) {
	fmt.Println("\n=== UUID去重性能测试结果 ===")
	fmt.Printf("%-12s %-10s %-12s %-15s %-10s %-10s %s\n", 
		"方法", "数据量", "耗时", "吞吐量(QPS)", "批次大小", "工作协程", "错误")
	fmt.Println(strings.Repeat("-", 80))
	
	for _, result := range results {
		errorMsg := ""
		if result.Error != "" {
			errorMsg = result.Error
		}
		
		fmt.Printf("%-12s %-10d %-12s %-15.2f %-10d %-10d %s\n",
			result.Method,
			result.DataSize,
			result.Duration.String(),
			result.ThroughputQPS,
			result.BatchSize,
			result.WorkerCount,
			errorMsg)
	}
	
	// 计算性能提升
	if len(results) >= 2 {
		fmt.Println("\n=== 性能提升分析 ===")
		baseResult := results[0]
		for i := 1; i < len(results); i++ {
			improvement := (results[i].ThroughputQPS - baseResult.ThroughputQPS) / baseResult.ThroughputQPS * 100
			fmt.Printf("%s 相比 %s 性能提升: %.2f%%\n", 
				results[i].Method, baseResult.Method, improvement)
		}
	}
}
