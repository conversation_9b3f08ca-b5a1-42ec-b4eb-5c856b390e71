package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务实现
type UserService struct {
	*BaseService
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) UserServiceInterface {
	return &UserService{
		BaseService: NewBaseService(db),
	}
}

// Login 用户登录
func (s *UserService) Login(ctx context.Context, username, password string) (*LoginResult, error) {
	var user User
	if err := s.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("用户名或密码错误")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, fmt.Errorf("用户已被禁用")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 生成JWT令牌
	token, err := s.generateJWTToken(&user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	return &LoginResult{
		Token: token,
		User:  &user,
	}, nil
}

// ValidateAPIKey 验证API密钥
func (s *UserService) ValidateAPIKey(ctx context.Context, apiKey string) (*User, error) {
	var user User
	if err := s.DB.Where("api_key = ? AND status = ?", apiKey, true).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("无效的API密钥")
		}
		return nil, fmt.Errorf("验证API密钥失败: %w", err)
	}

	return &user, nil
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(ctx context.Context, req *GetUsersRequest) (*GetUsersResponse, error) {
	var users []*User
	var total int64

	query := s.DB.Model(&User{})

	// 应用筛选条件
	if req.Username != "" {
		query = query.Where("username LIKE ?", "%"+req.Username+"%")
	}
	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取用户总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, fmt.Errorf("查询用户列表失败: %w", err)
	}

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	
	return &GetUsersResponse{
		PaginationResponse: PaginationResponse{
			Total:       total,
			Page:        req.Page,
			PageSize:    req.PageSize,
			TotalPages:  totalPages,
			HasNext:     req.Page < totalPages,
			HasPrevious: req.Page > 1,
		},
		Users: users,
	}, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(ctx context.Context, id uint) (*User, error) {
	var user User
	if err := s.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return &user, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// 检查用户名是否已存在
	var existingUser User
	if err := s.DB.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("用户名已存在")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查用户名失败: %w", err)
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		if err := s.DB.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
			return nil, fmt.Errorf("邮箱已存在")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("检查邮箱失败: %w", err)
		}
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 生成API密钥
	apiKey, err := s.generateAPIKey()
	if err != nil {
		return nil, fmt.Errorf("生成API密钥失败: %w", err)
	}

	// 创建用户 - 让GORM自动处理时间戳
	user := &User{
		Username: req.Username,
		Password: hashedPassword,
		Email:    req.Email,
		Role:     req.Role,
		Status:   true,
		APIKey:   apiKey,
	}

	if err := s.DB.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(ctx context.Context, id uint, req *UpdateUserRequest) error {
	// 检查用户是否存在
	var user User
	if err := s.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var existingUser User
		if err := s.DB.Where("email = ? AND id != ?", req.Email, id).First(&existingUser).Error; err == nil {
			return fmt.Errorf("邮箱已被其他用户使用")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("检查邮箱失败: %w", err)
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Role != "" {
		updates["role"] = req.Role
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	// 禁用自动时间戳更新
	if err := s.DB.Model(&user).UpdateColumns(updates).Error; err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}

	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(ctx context.Context, id uint) error {
	// 检查用户是否存在
	var user User
	if err := s.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 不能删除管理员用户（如果只有一个管理员）
	if user.IsAdmin() {
		var adminCount int64
		if err := s.DB.Model(&User{}).Where("role = ?", "admin").Count(&adminCount).Error; err != nil {
			return fmt.Errorf("检查管理员数量失败: %w", err)
		}
		if adminCount <= 1 {
			return fmt.Errorf("不能删除最后一个管理员用户")
		}
	}

	if err := s.DB.Delete(&user).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error {
	// 获取用户
	var user User
	if err := s.DB.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return fmt.Errorf("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := s.hashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码 - 禁用自动时间戳更新
	if err := s.DB.Model(&user).UpdateColumn("password", hashedPassword).Error; err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// ResetAPIKey 重置API密钥
func (s *UserService) ResetAPIKey(ctx context.Context, userID uint) (string, error) {
	// 检查用户是否存在
	var user User
	if err := s.DB.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", fmt.Errorf("用户不存在")
		}
		return "", fmt.Errorf("查询用户失败: %w", err)
	}

	// 生成新的API密钥
	apiKey, err := s.generateAPIKey()
	if err != nil {
		return "", fmt.Errorf("生成API密钥失败: %w", err)
	}

	// 更新API密钥 - 禁用自动时间戳更新
	if err := s.DB.Model(&user).UpdateColumn("api_key", apiKey).Error; err != nil {
		return "", fmt.Errorf("更新API密钥失败: %w", err)
	}

	return apiKey, nil
}

// 私有方法

// hashPassword 加密密码
func (s *UserService) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// generateAPIKey 生成API密钥
func (s *UserService) generateAPIKey() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateJWTToken 生成JWT令牌（需要实现JWT逻辑）
func (s *UserService) generateJWTToken(user *User) (string, error) {
	// TODO: 实现JWT令牌生成逻辑
	// 这里需要根据实际的JWT配置来实现
	return "jwt_token_placeholder", nil
}
