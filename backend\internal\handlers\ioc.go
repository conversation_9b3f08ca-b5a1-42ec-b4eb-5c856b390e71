package handlers

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// IOCHandler IOC情报处理器 - 统一管理IOC相关功能
type IOCHandler struct {
	BaseHandler
	db *gorm.DB
}

// NewIOCHandler 创建IOC处理器
func NewIOCHandler(db *gorm.DB) *IOCHandler {
	return &IOCHandler{
		db: db,
	}
}

// RegisterIOCRoutes 注册IOC相关路由
// 按照三个功能模块组织：数据采集、情报生产、情报推送
func (h *IOCHandler) RegisterIOCRoutes(r *gin.RouterGroup) {
	ioc := r.Group("/ioc")
	{
		// 1. 数据采集模块 (Data Collection)
		// 负责从各种数据接口采集原始数据，处理和清洗数据，存储为源数据
		dataCollection := ioc.Group("/data-collection")
		{
			dataCollection.GET("/source-data", h.GetIOCSourceData)                    // 获取IOC源数据列表
			dataCollection.POST("/source-data/generate", h.GenerateIOCSourceData)    // 从数据接口生成源数据
			dataCollection.DELETE("/source-data/batch", h.BatchDeleteIOCSourceData) // 批量删除源数据
			dataCollection.GET("/source-data/stats", h.GetIOCSourceDataStats)       // 源数据统计
		}

		// 2. 情报生产模块 (Intelligence Production)
		// 从源数据生成IOC情报，应用生产策略和过滤规则，管理IOC情报的CRUD操作
		intelligenceProduction := ioc.Group("/intelligence-production")
		{
			intelligenceProduction.GET("/intelligence", h.GetIOCIntelligence)                      // 获取IOC情报列表
			intelligenceProduction.GET("/intelligence/:id", h.GetIOCIntelligenceByID)             // 获取单个IOC情报
			intelligenceProduction.POST("/intelligence", h.CreateIOCIntelligence)                 // 创建IOC情报
			intelligenceProduction.PUT("/intelligence/:id", h.UpdateIOCIntelligence)              // 更新IOC情报
			intelligenceProduction.DELETE("/intelligence/:id", h.DeleteIOCIntelligence)           // 删除IOC情报
			intelligenceProduction.DELETE("/intelligence/batch", h.BatchDeleteIOCIntelligence)    // 批量删除IOC情报
			intelligenceProduction.POST("/intelligence/export", h.ExportIOCIntelligence)          // 导出IOC情报
			intelligenceProduction.POST("/intelligence/generate-from-source", h.GenerateIOCFromSource) // 从源数据生成IOC情报
			intelligenceProduction.GET("/production-strategy", h.GetProductionStrategyConfig)     // 获取生产策略配置
			intelligenceProduction.GET("/whitelist", h.GetIOCWhitelist)                          // 获取IOC白名单
			intelligenceProduction.GET("/stats", h.GetIOCIntelligenceStats)                      // IOC情报统计
			intelligenceProduction.POST("/tjun-query", h.QueryTJUNIOC)                           // 天际友盟IOC查询
			intelligenceProduction.POST("/weibu-query", h.QueryWeibuIOC)                          // 微步威胁情报IOC查询
		}

		// 3. 情报推送模块 (Intelligence Push)
		// 管理IOC情报的推送，支持单个和批量推送，推送记录管理
		intelligencePush := ioc.Group("/intelligence-push")
		{
			intelligencePush.POST("/single/:id", h.PushIOCIntelligence)                    // 单个IOC情报推送
			intelligencePush.POST("/batch", h.BatchPushIOCIntelligence)                    // 批量IOC情报推送
			intelligencePush.GET("/records", h.GetIOCIntelligencePushRecords)              // 获取推送记录
			intelligencePush.DELETE("/records/:id", h.DeleteIOCIntelligencePushRecord)     // 删除推送记录
			intelligencePush.GET("/records/stats", h.GetIOCIntelligencePushStats)          // 推送统计
		}
	}
}