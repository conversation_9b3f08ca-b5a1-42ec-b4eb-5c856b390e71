package main

import (
	"fmt"
	"log"
	"time"
	
	"vulnerability_push/internal/handlers"
	"vulnerability_push/internal/models"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// 示例：演示UUID去重多线程优化的使用方法
func main() {
	// 1. 初始化数据库连接（示例配置）
	dsn := "user:password@tcp(localhost:3306)/vulnerability_push_v2?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 2. 创建数据接口处理器
	dataInterfaceHandler := &handlers.DataInterfaceHandler{} // 简化示例
	baseInterface := handlers.NewBaseDataInterface(dataInterfaceHandler)

	// 3. 演示不同的配置使用方式
	demonstrateConfigurations(baseInterface)

	// 4. 演示性能测试
	demonstratePerformanceTesting(db)

	// 5. 演示实际使用场景
	demonstrateRealWorldUsage(baseInterface)
}

// demonstrateConfigurations 演示不同配置的使用
func demonstrateConfigurations(baseInterface *handlers.BaseDataInterface) {
	fmt.Println("=== 配置使用演示 ===")

	// 1. 使用预设配置
	fmt.Println("1. 使用高性能预设配置")
	baseInterface.SetDedupConfigByName("high")
	
	// 2. 根据数据量自动优化
	fmt.Println("2. 根据数据量自动优化配置")
	dataSize := 50000
	baseInterface.OptimizeDedupConfigForDataSize(dataSize)
	
	// 3. 使用自定义配置
	fmt.Println("3. 使用自定义配置")
	customConfig := &handlers.UUIDDedupConfig{
		BatchSize:     3000,
		WorkerCount:   8,
		SaveBatchSize: 3000,
	}
	baseInterface.SetDedupConfig(customConfig)

	// 4. 获取配置管理器信息
	fmt.Println("4. 配置管理器信息")
	configManager := handlers.GlobalUUIDDedupConfigManager
	configManager.PrintConfigInfo("auto")
	configManager.PrintConfigInfo("high")
}

// demonstratePerformanceTesting 演示性能测试
func demonstratePerformanceTesting(db *gorm.DB) {
	fmt.Println("\n=== 性能测试演示 ===")

	// 创建性能测试工具
	benchmark := handlers.NewUUIDDedupBenchmark(db)

	// 测试不同数据量的性能
	testSizes := []int{1000, 5000, 10000}
	existingRatio := 0.3 // 30%的UUID已存在

	for _, size := range testSizes {
		fmt.Printf("\n测试数据量: %d\n", size)
		
		results, err := benchmark.RunBenchmark(size, existingRatio)
		if err != nil {
			fmt.Printf("性能测试失败: %v\n", err)
			continue
		}

		benchmark.PrintBenchmarkResults(results)
	}
}

// demonstrateRealWorldUsage 演示实际使用场景
func demonstrateRealWorldUsage(baseInterface *handlers.BaseDataInterface) {
	fmt.Println("\n=== 实际使用场景演示 ===")

	// 模拟数据源采集场景
	simulateDataCollection(baseInterface)
}

// simulateDataCollection 模拟数据采集场景
func simulateDataCollection(baseInterface *handlers.BaseDataInterface) {
	fmt.Println("模拟CCCC黑科技数据采集场景...")

	// 1. 生成模拟数据
	mockRecords := generateMockRecords(10000)
	fmt.Printf("生成了 %d 条模拟记录\n", len(mockRecords))

	// 2. 提取UUID列表
	var uuids []string
	for _, record := range mockRecords {
		if record.UUID != "" {
			uuids = append(uuids, record.UUID)
		}
	}

	// 3. 根据数据量优化配置
	baseInterface.OptimizeDedupConfigForDataSize(len(uuids))

	// 4. 执行UUID去重检查（带性能测量）
	fmt.Println("开始UUID去重检查...")
	start := time.Now()
	
	processedMap, stats, err := baseInterface.MeasuredOptimizedBatchCheckUUIDsProcessed(uuids, "cccc_black_tech")
	if err != nil {
		fmt.Printf("UUID去重检查失败: %v\n", err)
		return
	}

	duration := time.Since(start)
	fmt.Printf("UUID去重检查完成，耗时: %v\n", duration)

	// 5. 统计结果
	unprocessedCount := 0
	var unprocessedUUIDs []string
	
	for uuid, processed := range processedMap {
		if !processed {
			unprocessedCount++
			unprocessedUUIDs = append(unprocessedUUIDs, uuid)
		}
	}

	fmt.Printf("统计结果:\n")
	fmt.Printf("  总UUID数量: %d\n", len(uuids))
	fmt.Printf("  已处理数量: %d\n", stats.ProcessedUUIDs)
	fmt.Printf("  未处理数量: %d\n", unprocessedCount)
	fmt.Printf("  处理方法: %s\n", stats.Method)
	fmt.Printf("  查询耗时: %v\n", stats.QueryDuration)

	// 6. 模拟批量保存新的UUID
	if len(unprocessedUUIDs) > 0 {
		fmt.Printf("批量保存 %d 个新UUID...\n", len(unprocessedUUIDs))
		saveStart := time.Now()
		
		err = baseInterface.BatchSaveProcessedUUIDs(unprocessedUUIDs, "cccc_black_tech", 1000)
		if err != nil {
			fmt.Printf("批量保存UUID失败: %v\n", err)
		} else {
			saveDuration := time.Since(saveStart)
			fmt.Printf("批量保存完成，耗时: %v\n", saveDuration)
		}
	}

	// 7. 性能总结
	fmt.Printf("\n性能总结:\n")
	fmt.Printf("  总处理时间: %v\n", duration)
	fmt.Printf("  平均QPS: %.2f\n", float64(len(uuids))/duration.Seconds())
	fmt.Printf("  去重效率: %.2f%%\n", float64(stats.ProcessedUUIDs)/float64(len(uuids))*100)
}

// MockRecord 模拟记录结构
type MockRecord struct {
	UUID      string
	SrcIP     string
	DstIP     string
	Timestamp int64
}

// generateMockRecords 生成模拟记录
func generateMockRecords(count int) []MockRecord {
	var records []MockRecord
	
	for i := 0; i < count; i++ {
		record := MockRecord{
			UUID:      fmt.Sprintf("mock-uuid-%d-%d", time.Now().Unix(), i),
			SrcIP:     fmt.Sprintf("192.168.1.%d", i%254+1),
			DstIP:     fmt.Sprintf("10.0.0.%d", i%254+1),
			Timestamp: time.Now().Unix(),
		}
		records = append(records, record)
	}
	
	return records
}

// 配置推荐函数
func getRecommendedConfigForDataSize(dataSize int) string {
	if dataSize < 1000 {
		return "low"
	} else if dataSize < 10000 {
		return "medium"
	} else if dataSize < 100000 {
		return "high"
	} else {
		return "ultra"
	}
}

// 性能监控函数
func monitorPerformance(stats *handlers.UUIDDedupPerformanceStats) {
	fmt.Printf("性能监控:\n")
	fmt.Printf("  处理方法: %s\n", stats.Method)
	fmt.Printf("  数据量: %d\n", stats.TotalUUIDs)
	fmt.Printf("  查询耗时: %v\n", stats.QueryDuration)
	fmt.Printf("  总耗时: %v\n", stats.TotalDuration)
	fmt.Printf("  批次大小: %d\n", stats.BatchSize)
	fmt.Printf("  工作协程: %d\n", stats.WorkerCount)
	
	// 性能告警
	if stats.QueryDuration > time.Second*5 {
		fmt.Printf("⚠️  警告: 查询耗时过长，建议优化配置\n")
	}
	
	if stats.TotalDuration > time.Second*10 {
		fmt.Printf("⚠️  警告: 总处理时间过长，建议增加工作协程数量\n")
	}
}
