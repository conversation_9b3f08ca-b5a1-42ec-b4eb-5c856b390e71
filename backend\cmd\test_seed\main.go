package main

import (
	"fmt"
	"log"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/database"
	"vulnerability_push/internal/utils"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	level := utils.ParseLogLevel(cfg.Log.Level)
	loggerInstance := utils.NewConsoleLogger(level, true)

	// 创建数据库管理器
	dbConfig := &database.DatabaseConfig{
		Type: cfg.Database.Type,
		MySQL: database.MySQLConfig{
			Host:     cfg.Database.MySQL.Host,
			Port:     cfg.Database.MySQL.Port,
			User:     cfg.Database.MySQL.User,
			Password: cfg.Database.MySQL.Password,
			Database: cfg.Database.MySQL.Database,
			Charset:  cfg.Database.MySQL.Charset,
			Loc:      cfg.Database.MySQL.Loc,
		},
	}
	dbManager := database.NewManager(dbConfig, loggerInstance)

	// 连接数据库
	if err := dbManager.DatabaseManager.Connect(); err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer dbManager.Close()

	// 初始化Seeder
	db := dbManager.GetDB()
	seeder := database.NewSeeder(db, loggerInstance)

	fmt.Println("开始测试默认数据填充...")

	// 检查当前数据
	var userCount, strategyCount int64
	db.Table("users").Count(&userCount)
	db.Table("production_strategies").Count(&strategyCount)
	fmt.Printf("填充前 - 用户数: %d, 生产策略数: %d\n", userCount, strategyCount)

	// 执行默认数据填充
	if err := seeder.SeedDefaultData(); err != nil {
		log.Fatalf("填充默认数据失败: %v", err)
	}

	// 检查填充后的数据
	db.Table("users").Count(&userCount)
	db.Table("production_strategies").Count(&strategyCount)
	fmt.Printf("填充后 - 用户数: %d, 生产策略数: %d\n", userCount, strategyCount)

	fmt.Println("默认数据填充测试完成！")
}
