package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/your-org/vulnerability-push-v2/internal/config"
	"github.com/your-org/vulnerability-push-v2/internal/database"
	"github.com/your-org/vulnerability-push-v2/internal/models"
	"github.com/your-org/vulnerability-push-v2/internal/utils"
)

func main() {
	// 命令行参数
	var (
		configPath = flag.String("config", "config.yaml", "配置文件路径")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	level := utils.ParseLogLevel(cfg.Log.Level)
	var loggerInstance utils.Logger
	if cfg.Log.File != "" {
		var err error
		loggerInstance, err = utils.NewFileLogger(cfg.Log.File, level, true)
		if err != nil {
			log.Fatalf("创建文件日志记录器失败: %v", err)
		}
	} else {
		loggerInstance = utils.NewConsoleLogger(level, true)
	}

	// 创建数据库管理器
	dbConfig := &database.DatabaseConfig{
		Type: cfg.Database.Type,
		MySQL: database.MySQLConfig{
			Host:     cfg.Database.MySQL.Host,
			Port:     cfg.Database.MySQL.Port,
			User:     cfg.Database.MySQL.User,
			Password: cfg.Database.MySQL.Password,
			Database: cfg.Database.MySQL.Database,
			Charset:  cfg.Database.MySQL.Charset,
			Loc:      cfg.Database.MySQL.Loc,
		},
		SQLite: database.SQLiteConfig{
			File:   cfg.Database.SQLite.File,
			Memory: cfg.Database.SQLite.Memory,
		},
	}
	dbManager := database.NewManager(dbConfig, loggerInstance)
	defer dbManager.Close()

	// 连接数据库
	if err := dbManager.Connect(); err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	db := dbManager.GetDB()

	fmt.Println("开始迁移IOC源数据表，添加处理状态字段...")

	// 检查字段是否已存在
	if db.Migrator().HasColumn(&models.IOCIntelligenceData{}, "processed_status") {
		fmt.Println("processed_status字段已存在，无需迁移")
	} else {
		fmt.Println("添加processed_status字段...")
		if err := db.Migrator().AddColumn(&models.IOCIntelligenceData{}, "processed_status"); err != nil {
			log.Fatalf("添加processed_status字段失败: %v", err)
		}
		fmt.Println("processed_status字段添加成功")
	}

	if db.Migrator().HasColumn(&models.IOCIntelligenceData{}, "processed_at") {
		fmt.Println("processed_at字段已存在，无需迁移")
	} else {
		fmt.Println("添加processed_at字段...")
		if err := db.Migrator().AddColumn(&models.IOCIntelligenceData{}, "processed_at"); err != nil {
			log.Fatalf("添加processed_at字段失败: %v", err)
		}
		fmt.Println("processed_at字段添加成功")
	}

	// 为现有数据设置默认值
	fmt.Println("为现有数据设置默认值...")
	result := db.Model(&models.IOCIntelligenceData{}).
		Where("processed_status IS NULL OR processed_status = ''").
		Updates(map[string]interface{}{
			"processed_status": "unprocessed",
			"processed_at":     0,
		})

	if result.Error != nil {
		log.Fatalf("设置默认值失败: %v", result.Error)
	}

	fmt.Printf("迁移完成！更新了 %d 条记录\n", result.RowsAffected)

	// 显示统计信息
	var total, unprocessed, processed int64
	db.Model(&models.IOCIntelligenceData{}).Count(&total)
	db.Model(&models.IOCIntelligenceData{}).Where("processed_status = 'unprocessed'").Count(&unprocessed)
	db.Model(&models.IOCIntelligenceData{}).Where("processed_status = 'processed'").Count(&processed)

	fmt.Printf("\n统计信息:\n")
	fmt.Printf("总记录数: %d\n", total)
	fmt.Printf("未处理: %d\n", unprocessed)
	fmt.Printf("已处理: %d\n", processed)
}
