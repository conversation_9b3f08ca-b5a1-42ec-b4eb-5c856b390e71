package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"vulnerability_push/internal/config"
	"vulnerability_push/internal/database"
	"vulnerability_push/internal/models"
	"vulnerability_push/internal/utils"
)

func main() {
	// 命令行参数
	var (
		configPath = flag.String("config", "config.yaml", "配置文件路径")
		skipSeed   = flag.Bool("skip-seed", false, "跳过默认数据填充")
		dropTables = flag.Bool("drop-tables", false, "删除所有表后重新创建")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	level := utils.ParseLogLevel(cfg.Log.Level)
	var loggerInstance utils.Logger
	if cfg.Log.File != "" {
		var err error
		loggerInstance, err = utils.NewFileLogger(cfg.Log.File, level, true)
		if err != nil {
			log.Fatalf("创建文件日志记录器失败: %v", err)
		}
	} else {
		loggerInstance = utils.NewConsoleLogger(level, true)
	}

	// 创建数据库管理器
	dbConfig := &database.DatabaseConfig{
		Type: cfg.Database.Type,
		MySQL: database.MySQLConfig{
			Host:     cfg.Database.MySQL.Host,
			Port:     cfg.Database.MySQL.Port,
			User:     cfg.Database.MySQL.User,
			Password: cfg.Database.MySQL.Password,
			Database: cfg.Database.MySQL.Database,
			Charset:  cfg.Database.MySQL.Charset,
			Loc:      cfg.Database.MySQL.Loc,
		},
		SQLite: database.SQLiteConfig{
			File:   cfg.Database.SQLite.File,
			Memory: cfg.Database.SQLite.Memory,
		},
	}
	dbManager := database.NewManager(dbConfig, loggerInstance)
	defer dbManager.Close()

	// 如果需要删除表
	if *dropTables {
		fmt.Println("警告：即将删除所有数据库表！")
		fmt.Print("请输入 'YES' 确认删除: ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "YES" {
			fmt.Println("操作已取消")
			os.Exit(0)
		}

		if err := dropAllTables(dbManager); err != nil {
			log.Fatalf("删除表失败: %v", err)
		}
		fmt.Println("所有表已删除")
	}

	// 获取所有模型
	models := models.GetAllModels()

	// 初始化数据库
	fmt.Println("开始初始化数据库...")
	if err := dbManager.Initialize(models, *skipSeed); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	fmt.Println("数据库初始化完成！")

	// 显示统计信息
	if err := showDatabaseStats(dbManager); err != nil {
		log.Printf("获取数据库统计信息失败: %v", err)
	}
}

// dropAllTables 删除所有表
func dropAllTables(dbManager *database.Manager) error {
	db := dbManager.GetDB()
	
	// 获取所有表名
	var tables []string
	if err := db.Raw("SHOW TABLES").Scan(&tables).Error; err != nil {
		return fmt.Errorf("获取表列表失败: %v", err)
	}

	// 禁用外键检查
	if err := db.Exec("SET FOREIGN_KEY_CHECKS = 0").Error; err != nil {
		return fmt.Errorf("禁用外键检查失败: %v", err)
	}

	// 删除所有表
	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS `%s`", table)).Error; err != nil {
			return fmt.Errorf("删除表 %s 失败: %v", table, err)
		}
		fmt.Printf("已删除表: %s\n", table)
	}

	// 启用外键检查
	if err := db.Exec("SET FOREIGN_KEY_CHECKS = 1").Error; err != nil {
		return fmt.Errorf("启用外键检查失败: %v", err)
	}

	return nil
}

// showDatabaseStats 显示数据库统计信息
func showDatabaseStats(dbManager *database.Manager) error {
	db := dbManager.GetDB()

	fmt.Println("\n=== 数据库统计信息 ===")

	// 获取所有表名
	rows, err := db.Raw(`
		SELECT table_name
		FROM information_schema.tables
		WHERE table_schema = DATABASE()
		ORDER BY table_name
	`).Rows()
	if err != nil {
		return fmt.Errorf("查询表列表失败: %v", err)
	}
	defer rows.Close()

	var tableNames []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return fmt.Errorf("读取表名失败: %v", err)
		}
		tableNames = append(tableNames, tableName)
	}

	fmt.Printf("%-30s %s\n", "表名", "记录数")
	fmt.Println(strings.Repeat("-", 40))

	totalRows := int64(0)
	for _, tableName := range tableNames {
		var count int64
		if err := db.Table(tableName).Count(&count).Error; err != nil {
			fmt.Printf("%-30s %s\n", tableName, "查询失败")
			continue
		}
		fmt.Printf("%-30s %d\n", tableName, count)
		totalRows += count
	}

	fmt.Println(strings.Repeat("-", 40))
	fmt.Printf("%-30s %d\n", "总计", totalRows)
	fmt.Printf("表数量: %d\n", len(tableNames))

	return nil
}
