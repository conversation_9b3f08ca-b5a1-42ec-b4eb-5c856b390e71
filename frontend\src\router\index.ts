import { createRouter, createWebHistory } from 'vue-router'
import Login from '../Login.vue'
import Home from '../Home.vue'
import UserManagement from '../UserManagement.vue'
import VulnerabilityList from '../VulnerabilityList.vue'
import VulnerabilityForm from '../VulnerabilityForm.vue'
import PushManagement from '../PushManagement.vue'
import CrawlerManagement from '../CrawlerManagement.vue'
// IOC情报功能模块 - 按三个功能模块重新组织
import IOCIntelligence from '../IOCIntelligence.vue'  // 情报生产模块
import IOCIntelligenceData from '../IOCIntelligenceData.vue'  // 数据采集模块 (源数据管理)
import IOCIntelligencePushManagement from '../IOCIntelligencePushManagement.vue'  // 情报推送模块
import IOCWhitelist from '../IOCWhitelist.vue'
import DataInterface from '../DataInterface.vue'
import ProductionStrategy from '../ProductionStrategy.vue'

// 路由配置
const routes = [
  { 
    path: '/', 
    component: Login
  },
  { 
    path: '/home', 
    component: Home
  },
  { 
    path: '/users', 
    component: UserManagement
  },
  {
    path: '/vulnerabilities',
    component: VulnerabilityList
  },
  {
    path: '/vulnerability/create',
    component: VulnerabilityForm
  },
  {
    path: '/vulnerability/edit/:id',
    component: VulnerabilityForm
  },
  {
    path: '/push',
    component: PushManagement
  },
  {
    path: '/crawlers',
    component: CrawlerManagement
  },
  // IOC情报功能模块路由 - 按三个功能模块重新组织
  {
    path: '/ioc-data-collection',
    component: IOCIntelligenceData,
    meta: { title: 'IOC源数据管理', module: '数据采集模块' }
  },
  {
    path: '/ioc-intelligence-production',
    component: IOCIntelligence,
    meta: { title: 'IOC情报生产管理', module: '情报生产模块' }
  },
  {
    path: '/ioc-intelligence-push',
    component: IOCIntelligencePushManagement,
    meta: { title: 'IOC情报推送管理', module: '情报推送模块' }
  },
  {
    path: '/production-strategy',
    component: ProductionStrategy,
    meta: { title: '生产策略配置', module: '情报生产模块' }
  },
  {
    path: '/ioc-whitelist',
    component: IOCWhitelist,
    meta: { title: 'IOC白名单管理', module: '情报生产模块' }
  },
  {
    path: '/data-interface',
    component: DataInterface,
    meta: { title: '数据接口管理', module: '数据采集模块' }
  },
  // 兼容性路由 - 保持向后兼容
  {
    path: '/ioc-intelligence',
    redirect: '/ioc-intelligence-production'
  },
  {
    path: '/ioc-intelligence-data',
    redirect: '/ioc-data-collection'
  },
  {
    path: '/ip-collection',
    redirect: '/production-strategy'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 简化版本用于调试
router.beforeEach(async (to, _from, next) => {
  const token = localStorage.getItem('token')
  console.log('路由守卫:', { to: to.path, from: _from.path, hasToken: !!token })

  // 暂时简化路由守卫逻辑，只处理基本的登录重定向
  if (to.path === '/' && token) {
    console.log('有token且访问登录页，跳转到首页')
    next('/home')
    return
  }

  // 其他情况都直接通过
  console.log('直接通过路由守卫')
  next()
})

export default router 