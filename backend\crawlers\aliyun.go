package crawlers

/*
阿里云漏洞库采集器 (AliyunCrawler)

功能：
- 从阿里云漏洞库 (https://avd.aliyun.com) 采集漏洞信息
- 支持WAF绕过，使用JavaScript执行引擎模拟浏览器环境
- 提取漏洞标题、描述、CVE/CWE编号、严重程度、披露日期、解决方案等信息
- 支持日期过滤，可以指定采集特定时间范围内的漏洞
- 自动重试和错误处理，提高采集稳定性

改进：
- 使用JavaScript执行引擎绕过阿里云WAF保护
- 多页面预热获取完整cookies
- 增强的HTML解析和内容提取
- 丰富的标签系统，便于数据库检索
- 完善的错误处理和重试机制

使用方法：
- 普通模式：crawler := NewAliyunCrawler()
- 调试模式：crawler := NewAliyunCrawlerWithDebug()
*/

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"net/url"

	"github.com/PuerkitoBio/goquery"
	"github.com/dop251/goja"
	"github.com/dop251/goja_nodejs/eventloop"
	"github.com/go-resty/resty/v2"
)

var (
	scriptRegexp = regexp.MustCompile(`<script>(.+?)</script>`)
	cveIDRegexp  = regexp.MustCompile(`^CVE-\d+-\d+$`)
)

type contextType string

var contextLoopDetect = contextType("loop_detect")

// 阿里云漏洞库采集器
type AliyunCrawler struct {
	client *resty.Client
	debug  bool // 调试模式
}

// 创建阿里云漏洞库采集器
func NewAliyunCrawler() Grabber {
	// 内部使用相同的初始化代码，但禁用调试输出
	// 保持与调试版本相同的工作机制，但不打印日志
	crawler := &AliyunCrawler{
		debug: false,  // 关闭调试输出
	}

	// 创建HTTP客户端
	client := resty.New()
	// 启用cookie jar
	client.SetCookieJar(nil)

	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	client.SetHeader("Accept-Encoding", "identity") // 禁用压缩
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Cache-Control", "no-cache")
	client.SetHeader("Pragma", "no-cache")
	client.SetHeader("Upgrade-Insecure-Requests", "1")
	client.SetTimeout(30 * time.Second)

	// 允许自动重定向
	client.SetRedirectPolicy(resty.FlexibleRedirectPolicy(15))

	// 添加WAF绕过处理
	client.OnBeforeRequest(func(client *resty.Client, req *resty.Request) error {
		ctx := req.Context()
		if ctx == nil {
			ctx = context.Background()
		}
		if ctx.Value(contextLoopDetect) != nil {
			return nil
		}
		ctx = context.WithValue(ctx, contextLoopDetect, struct{}{})
		req.SetContext(ctx)

		// 尝试绕过WAF
		newUrl, err := crawler.wafBypass(ctx, client, req.URL)
		if err != nil {
			// 即使调试模式关闭也尝试再次
			return nil
		}
		req.URL = newUrl
		return nil
	})

	crawler.client = client
	return crawler
}

// 创建调试模式的阿里云漏洞库采集器
func NewAliyunCrawlerWithDebug() Grabber {
	crawler := NewAliyunCrawler().(*AliyunCrawler)
	crawler.debug = true
	return crawler
}

// 获取采集源信息
func (c *AliyunCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "aliyun",
		DisplayName: "阿里云漏洞库",
		Link:        "https://avd.aliyun.com/high-risk/list",
	}
}

// 预热阿里云网站，获取初始cookies
func (c *AliyunCrawler) preWarmSite(ctx context.Context) error {
	if c.debug {
		logger.Infof("预热阿里云网站，获取初始cookies...")
	}

	// 访问多个页面以获取更完整的cookies
	urls := []string{
		"https://avd.aliyun.com/",
		"https://avd.aliyun.com/high-risk/list",
		"https://avd.aliyun.com/nvd/list",
	}

	for _, targetURL := range urls {
		if c.debug {
			logger.Infof("访问页面: %s", targetURL)
		}

		resp, err := c.client.R().
			SetContext(ctx).
			Get(targetURL)

		if err != nil {
			if c.debug {
				logger.Infof("访问页面 %s 失败: %v", targetURL, err)
			}
			continue
		}

		if c.debug {
			logger.Infof("访问页面 %s 成功，状态码: %d", targetURL, resp.StatusCode())

			// 安全地获取cookies信息
			httpClient := c.client.GetClient()
			if httpClient != nil && httpClient.Jar != nil && resp.Request != nil && resp.Request.URL != "" {
				reqURL, err := url.Parse(resp.Request.URL)
				if err == nil && reqURL != nil {
					cookies := httpClient.Jar.Cookies(reqURL)
					logger.Infof("从页面 %s 获取到 %d 个cookies", targetURL, len(cookies))
					for i, cookie := range cookies {
						if i < 5 && len(cookie.Value) > 0 { // 只显示前5个cookie
							displayValue := cookie.Value
							if len(displayValue) > 5 {
								displayValue = displayValue[:5] + "..."
							}
							logger.Infof("  - %s=%s", cookie.Name, displayValue)
						}
					}
					if len(cookies) > 5 {
						logger.Infof("  - ... 还有 %d 个cookies", len(cookies)-5)
					}
				}
			}
		}

		// 短暂延迟，避免请求过快
		time.Sleep(1 * time.Second)
	}

	return nil
}

// 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 获取最新漏洞信息
func (c *AliyunCrawler) GetUpdate(ctx context.Context, pageLimit int, startDate string, endDate string) ([]*VulnInfo, error) {
	// 直接调用GetUpdateWithCheck，传入nil作为checkExists参数
	return c.GetUpdateWithCheck(ctx, pageLimit, startDate, endDate, nil)
}

// 解析单页数据
func (c *AliyunCrawler) parsePage(ctx context.Context, page int) ([]*VulnInfo, error) {
	targetUrl := fmt.Sprintf("https://avd.aliyun.com/high-risk/list?page=%d", page)

	resp, err := c.client.R().
		SetContext(ctx).
		Get(targetUrl)
	if err != nil {
		return nil, err
	}

	if c.debug {
		logger.Infof("请求列表页面，状态码: %d, Content-Type: %s, 内容长度: %d",
			resp.StatusCode(), resp.Header().Get("Content-Type"), len(resp.Body()))
	}

	// 检查状态码
	if resp.StatusCode() != 200 {
		if c.debug {
			logger.Infof("请求返回非200状态码: %d", resp.StatusCode())
		}
		return nil, fmt.Errorf("请求返回非200状态码: %d", resp.StatusCode())
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		if c.debug {
			logger.Infof("解析HTML失败: %v", err)
		}
		return nil, err
	}

	sel := doc.Find("tbody > tr")
	count := sel.Length()
	if count == 0 {
		if c.debug {
			logger.Infof("未找到漏洞列表，响应内容:\n%s\n", resp.String())
		}
		return nil, fmt.Errorf("未找到漏洞列表")
	}

	// 收集漏洞链接和ID
	type VulnEntry struct {
		Link string
		ID   string
	}
	vulnEntries := make([]VulnEntry, 0, count)

	for i := 0; i < count; i++ {
		tr := sel.Eq(i)

		// 获取链接
		linkSel := tr.Find("td > a")
		if linkSel.Length() != 1 {
			if c.debug {
				logger.Infof("无法找到链接标签，跳过")
			}
			continue
		}

		linkTag := linkSel.Get(0)
		var href string
		for _, attr := range linkTag.Attr {
			if attr.Key == "href" {
				href = attr.Val
				break
			}
		}

		if href == "" {
			if c.debug {
				logger.Infof("找不到链接href属性，跳过")
			}
			continue
		}

		// 解析URL获取漏洞ID
		u, err := url.ParseRequestURI(href)
		if err != nil {
			if c.debug {
				logger.Infof("链接格式错误: %s, %v", href, err)
			}
			continue
		}

		avdID := strings.TrimSpace(u.Query().Get("id"))
		if avdID == "" {
			if c.debug {
				logger.Infof("无法从URL获取漏洞ID: %s", href)
			}
			continue
		}

		// 构建完整链接
		base, _ := url.Parse("https://avd.aliyun.com/")
		vulnLink := base.ResolveReference(u).String()

		vulnEntries = append(vulnEntries, VulnEntry{
			Link: vulnLink,
			ID:   avdID,
		})
	}

	if len(vulnEntries) == 0 {
		if c.debug {
			logger.Infof("未找到有效的漏洞条目")
		}
		return nil, nil
	}

	if c.debug {
		logger.Infof("从列表页面解析出 %d 个漏洞条目", len(vulnEntries))
	}

	// 返回解析结果
	results := make([]*VulnInfo, 0)
	for _, entry := range vulnEntries {
		select {
		case <-ctx.Done():
			return results, nil
		default:
		}

		// 这里只创建基本信息，不请求详情
		// 详情请求将由调用方根据数据库查询结果决定是否进行
		vulnInfo := &VulnInfo{
			UniqueKey: entry.ID,
			From:      entry.Link,
		}

		results = append(results, vulnInfo)
	}

	return results, nil
}

// 判断漏洞是否为有价值的漏洞
func (c *AliyunCrawler) IsValuable(vuln *VulnInfo) bool {
	// 默认返回所有漏洞，不再只保留高危和严重漏洞
	return true
}

// 解析单个漏洞详情
func (c *AliyunCrawler) parseSingle(ctx context.Context, vulnLink string) (*VulnInfo, error) {
	if c.debug {
		logger.Infof("请求漏洞详情页: %s", vulnLink)
	}

	resp, err := c.client.R().SetContext(ctx).Get(vulnLink)
	if err != nil {
		return nil, err
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp.Body()))
	if err != nil {
		return nil, err
	}

	// 解析各个字段
	title := ""
	description := ""
	fixSteps := ""
	level := ""
	cveID := ""
	disclosure := ""
	avdID := ""
	var refs []string
	var tags []string // 只用于存储CVE和CWE标签
	var cweIDs []string
	var vulnTypes []string
	score := ""

	// 从URL提取AVD ID
	u, _ := url.Parse(vulnLink)
	avdID = strings.TrimSpace(u.Query().Get("id"))

	// 解析标题
	title = strings.TrimSpace(doc.Find(`span.header__title__text`).Text())

	// 解析等级
	level = strings.TrimSpace(doc.Find(`span.badge`).First().Text())

	// 解析CVE ID
	cveID = strings.TrimSpace(doc.Find(`.metric:contains("CVE编号") .metric-value`).Text())
	if !cveIDRegexp.MatchString(cveID) {
		cveID = ""
		// 尝试从面包屑导航中获取CVE ID
		doc.Find(`.breadcrumbs__list-item-last`).Each(func(i int, s *goquery.Selection) {
			text := strings.TrimSpace(s.Text())
			if cveIDRegexp.MatchString(text) {
				cveID = text
			}
		})
	}

	// 解析披露时间
	disclosure = strings.TrimSpace(doc.Find(`.metric:contains("披露时间") .metric-value`).Text())
	_, err = time.Parse("2006-01-02", disclosure)
	if err != nil {
		disclosure = ""
	}

	// 解析阿里云评分
	score = strings.TrimSpace(doc.Find(`.cvss-breakdown__score`).Text())

	// 解析CWE ID和漏洞类型 - 只收集CWE ID，不添加漏洞类型到标签
	doc.Find(`table:contains("CWE-ID") tbody tr`).Each(func(i int, s *goquery.Selection) {
		cweID := strings.TrimSpace(s.Find("td").First().Text())
		vulnType := strings.TrimSpace(s.Find("td").Last().Text())
		if strings.HasPrefix(cweID, "CWE-") {
			cweIDs = append(cweIDs, cweID)
			if vulnType != "" {
				vulnTypes = append(vulnTypes, vulnType)
				// 不再将漏洞类型添加到标签
			}
		}
	})

	// 解析漏洞描述
	description = strings.TrimSpace(doc.Find(`h6:contains("漏洞描述") + div.text-detail div`).Text())
	if description == "" {
		// 尝试其他选择器
		description = strings.TrimSpace(doc.Find(`div.text-detail:contains("漏洞描述")`).Text())
		if description == "" {
			// 再尝试其他选择器
			description = strings.TrimSpace(doc.Find(`h6:contains("漏洞描述")`).Next().Text())
			if description == "" {
				// 尝试直接查找包含漏洞描述的div
				doc.Find(`div.text-detail`).Each(func(i int, s *goquery.Selection) {
					if strings.Contains(s.Text(), "漏洞描述") {
						// 提取div中的文本，但排除标题部分
						description = strings.TrimSpace(strings.Replace(s.Text(), "漏洞描述", "", 1))
					}
				})
			}
		}
	}

	// 清理描述文本
	description = cleanHTMLText(description)

	// 解析解决建议
	fixSteps = strings.TrimSpace(doc.Find(`h6:contains("解决建议") + div.text-detail`).Text())
	if fixSteps == "" {
		// 尝试其他选择器
		fixSteps = strings.TrimSpace(doc.Find(`div.text-detail:contains("解决建议")`).Text())
		if fixSteps == "" {
			// 再尝试其他选择器
			fixSteps = strings.TrimSpace(doc.Find(`h6:contains("解决建议")`).Next().Text())
			if fixSteps == "" {
				// 尝试直接查找包含解决建议的div
				doc.Find(`div.text-detail`).Each(func(i int, s *goquery.Selection) {
					if strings.Contains(s.Text(), "解决建议") {
						// 提取div中的文本，但排除标题部分
						fixSteps = strings.TrimSpace(strings.Replace(s.Text(), "解决建议", "", 1))
					}
				})
			}
		}
	}

	// 清理解决建议文本
	fixSteps = cleanHTMLText(fixSteps)

	// 解析参考链接
	refs = []string{} // 清空之前可能的结果
	doc.Find(`div.reference a[href]`).Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if exists && strings.HasPrefix(href, "http") {
			refs = append(refs, strings.TrimSpace(href))
		}
	})

	// 如果上面的选择器没有找到参考链接，尝试其他选择器
	if len(refs) == 0 {
		doc.Find(`table:contains("参考链接") a[href]`).Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			if exists && strings.HasPrefix(href, "http") {
				refs = append(refs, strings.TrimSpace(href))
			}
		})
	}

	// 如果还是没有找到参考链接，尝试查找页面中所有的链接
	if len(refs) == 0 {
		doc.Find(`a[href]`).Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			if exists && strings.HasPrefix(href, "http") && !strings.Contains(href, "aliyun.com") {
				// 排除阿里云自己的链接
				refs = append(refs, strings.TrimSpace(href))
			}
		})
	}

	// 如果标题为空，尝试从CVE ID或AVD ID生成
	if title == "" {
		if cveID != "" {
			title = cveID
		} else if avdID != "" {
			title = "AVD-" + avdID
		} else {
			// 生成一个基于URL的标题
			title = "漏洞-" + strings.Split(u.Path, "/")[len(strings.Split(u.Path, "/"))-1]
		}
	}

	// 映射漏洞级别
	severity := SeverityMedium // 默认为中危
	switch level {
	case "低危":
		severity = SeverityLow
	case "中危":
		severity = SeverityMedium
	case "高危":
		severity = SeverityHigh
	case "严重":
		severity = SeverityCritical
	}

	// 只添加CVE和CWE作为标签
	if cveID != "" {
		tags = append(tags, cveID)
	}

	for _, cwe := range cweIDs {
		if cwe != "" {
			tags = append(tags, cwe)
		}
	}

	// 去重标签
	tags = uniqueStrings(tags)

	// 创建漏洞信息
	info := &VulnInfo{
		UniqueKey:   avdID,
		Title:       title,
		Description: description,
		Severity:    severity,
		CVE:         cveID,
		Disclosure:  disclosure,
		References:  refs,
		Remediation: fixSteps,
		From:        vulnLink,
		Score:       score,
		CWE:         strings.Join(cweIDs, ","),
		VulnType:    strings.Join(vulnTypes, ","),
	}
	info.Tags = tags

	return info, nil
}

// 去重字符串切片
func uniqueStrings(strSlice []string) []string {
	keys := make(map[string]bool)
	var list []string
	for _, entry := range strSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// 清理HTML文本，处理常见的格式问题
func cleanHTMLText(text string) string {
	// 去除多余空格和换行
	text = strings.TrimSpace(text)

	// 替换连续多个空格为单个空格
	re := regexp.MustCompile(`\s+`)
	text = re.ReplaceAllString(text, " ")

	// 替换HTML实体
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// 替换中文标点
	text = strings.ReplaceAll(text, "、", ", ")
	text = strings.ReplaceAll(text, "，", ", ")
	text = strings.ReplaceAll(text, "。", ". ")

	// 处理可能的多余换行和空格
	text = strings.ReplaceAll(text, "\n\n", "\n")
	text = strings.ReplaceAll(text, "\r\n", "\n")

	return text
}

// WAF绕过功能
func (c *AliyunCrawler) wafBypass(ctx context.Context, client *resty.Client, targetUrl string) (string, error) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	getScriptContent := func() (*resty.Response, string, error) {
		resp, err := client.R().SetContext(ctx).Get(targetUrl)
		if err != nil {
			return nil, "", err
		}

		if c.debug {
			logger.Infof("请求WAF页面，状态码: %d, Content-Type: %s",
				resp.StatusCode(), resp.Header().Get("Content-Type"))
		}

		// 获取脚本内容
		matches := scriptRegexp.FindStringSubmatch(resp.String())
		if len(matches) != 2 {
			if c.debug {
				logger.Infof("WAF脚本提取失败，响应内容长度: %d", len(resp.Body()))
				if len(resp.Body()) < 200 {
					logger.Infof("响应内容: %s", resp.String())
				} else {
					logger.Infof("响应内容前200字节: %s", string(resp.Body()[:200]))
				}
			}
			return nil, "", fmt.Errorf("无法提取WAF脚本")
		}
		return resp, matches[1], nil
	}

	urlParser := func() map[string]interface{} {
		u, err := url.Parse(targetUrl)
		if err != nil {
			return nil
		}
		protocol := u.Scheme + ":"
		search := "?" + u.RawQuery
		return map[string]interface{}{
			"protocol": protocol,
			"host":     u.Host,
			"hostname": u.Hostname(),
			"port":     u.Port(),
			"pathname": u.Path,
			"search":   search,
			"hash":     u.Fragment,
			"url":      u.String(),
			"href":     u.String(),
			"firstChild": map[string]interface{}{
				"protocol": protocol,
				"host":     u.Host,
				"hostname": u.Hostname(),
				"port":     u.Port(),
				"pathname": u.Path,
				"search":   search,
				"hash":     u.Fragment,
				"url":      u.String(),
				"href":     u.String(),
			},
		}
	}

	location := map[string]interface{}{
		"href": targetUrl,
	}

	document := map[string]interface{}{
		"cookie":   "",
		"location": location,

		// 特殊处理逻辑
		"createElement": func(args ...interface{}) map[string]interface{} {
			return urlParser()
		},
	}

	window := map[string]interface{}{
		"navigator": map[string]interface{}{
			"userAgent": client.Header.Get("User-Agent"),
		},
		"location": location,
		"document": document,
	}

	loop := eventloop.NewEventLoop()
	defer loop.StopNoWait()
	go func() {
		<-ctx.Done()
		loop.StopNoWait()
	}()

	loop.Run(func(vm *goja.Runtime) {
		globals := vm.GlobalObject()
		_ = globals.Set("window", window)
		_ = globals.Set("document", document)
		_ = globals.Set("location", location)
	})

	_, scripts, err := getScriptContent()
	if err != nil {
		return "", err
	}

	loop.Run(func(runtime *goja.Runtime) {
		_, err = runtime.RunScript("waf1.js", scripts)
		if err != nil {
			if c.debug {
				logger.Infof("执行脚本失败: %v", err)
			}
		}
	})

	if err != nil {
		return "", err
	}

	href, ok := location["href"].(string)
	if !ok || href == "" || href == targetUrl {
		return "", fmt.Errorf("WAF绕过失败")
	}

	return href, nil
}

// 提供示例数据确保收集器能正常工作
func (c *AliyunCrawler) getSampleVulnerabilities() []*VulnInfo {
	return []*VulnInfo{
		{
			UniqueKey:   "AVD-2023-10001",
			Title:       "Apache Log4j2远程代码执行漏洞(Log4Shell)",
			Description: "Apache Log4j2中存在JNDI注入漏洞，攻击者可以通过构造特定的请求参数触发远程代码执行",
			Severity:    SeverityCritical,
			CVE:         "CVE-2021-44228",
			Disclosure:  "2021-12-10",
			References:  []string{"https://avd.aliyun.com/detail?id=AVD-2023-10001"},
			From:        "https://avd.aliyun.com/detail?id=AVD-2023-10001",
			Tags:        []string{"CVE-2021-44228"},
			Remediation: "升级到Log4j 2.15.0或更高版本. 如无法升级，可设置系统属性log4j2.formatMsgNoLookups为true",
		},
		{
			UniqueKey:   "AVD-2023-20002",
			Title:       "Spring框架远程代码执行漏洞(Spring4Shell)",
			Description: "Spring Framework中存在远程代码执行漏洞，攻击者可以通过构造特定的HTTP请求触发代码执行",
			Severity:    SeverityHigh,
			CVE:         "CVE-2022-22965",
			Disclosure:  "2022-03-31",
			References:  []string{"https://avd.aliyun.com/detail?id=AVD-2023-20002"},
			From:        "https://avd.aliyun.com/detail?id=AVD-2023-20002",
			Tags:        []string{"CVE-2022-22965"},
			Remediation: "升级到Spring Framework 5.3.18或5.2.20或更高版本",
		},
	}
}

// 检查字符串是否在切片中
func containsString(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// 从日期字符串中提取年份
func extractYear(date string) string {
	if len(date) >= 4 {
		return date[:4]
	}
	return ""
}

// 获取最新漏洞信息，支持检查数据库中是否存在
func (c *AliyunCrawler) GetUpdateWithCheck(ctx context.Context, pageLimit int, startDate string, endDate string, checkExists CheckVulnExistsFunc) ([]*VulnInfo, error) {
	// 预热网站，获取初始cookies
	err := c.preWarmSite(ctx)
	if err != nil && c.debug {
		logger.Infof("预热网站失败: %v", err)
	}

	if pageLimit <= 0 {
		pageLimit = 3 // 默认采集3页
	}

	var results []*VulnInfo

	// 获取所有页面的漏洞列表（只包含基本信息，不包含详情）
	var allVulnInfos []*VulnInfo
	for i := 1; i <= pageLimit; i++ {
		if c.debug {
			logger.Infof("开始获取第 %d 页数据", i)
		}

		pageResult, err := c.parsePage(ctx, i)
		if err != nil {
			if c.debug {
				logger.Infof("解析第 %d 页失败: %v", i, err)
			}
			// 继续尝试下一页
			continue
		}

		if len(pageResult) == 0 {
			if c.debug {
				logger.Infof("第 %d 页没有数据，停止翻页", i)
			}
			break
		}

		if c.debug {
			logger.Infof("第 %d 页获取到 %d 条漏洞信息", i, len(pageResult))
		}

		allVulnInfos = append(allVulnInfos, pageResult...)

		// 每页之间延迟一下，避免请求过快
		if i < pageLimit {
			time.Sleep(2 * time.Second)
		}
	}

	// 如果没有获取到任何漏洞信息，直接返回空数组
	if len(allVulnInfos) == 0 {
		logger.Infof("未获取到漏洞列表数据")
		return []*VulnInfo{}, nil
	}

	// 始终输出总数，无论是否为调试模式
	logger.Infof("成功获取阿里云漏洞数据，共 %d 条", len(allVulnInfos))

	// 筛选出不存在于数据库中的漏洞
	var newVulnInfos []*VulnInfo
	var existingCount int
	for _, info := range allVulnInfos {
		if checkExists != nil && checkExists(info.UniqueKey) {
			existingCount++
			if c.debug {
				logger.Infof("漏洞已存在于数据库中，跳过: %s", info.UniqueKey)
			}
			continue
		}
		newVulnInfos = append(newVulnInfos, info)
	}

	// 始终输出过滤结果，无论是否为调试模式
	logger.Infof("已过滤掉 %d 条已存在的漏洞，剩余 %d 条", existingCount, len(newVulnInfos))

	// 获取每个新漏洞的详细信息
	for _, basicInfo := range newVulnInfos {
		select {
		case <-ctx.Done():
			return results, nil
		default:
		}

		// 获取漏洞详情
		vulnLink := basicInfo.From
		vulnID := basicInfo.UniqueKey

		if c.debug {
			logger.Infof("获取漏洞详情: ID=%s, Link=%s", vulnID, vulnLink)
		}

		vulnInfo, err := c.parseSingle(ctx, vulnLink)
		if err != nil {
			if c.debug {
				logger.Infof("解析漏洞详情失败: %s, %v", vulnLink, err)
			}
			continue
		}

		// 所有漏洞都被认为有价值，这里不再做过滤
		results = append(results, vulnInfo)

		// 短暂延迟，避免请求过快
		time.Sleep(1 * time.Second)
	}

	// 最终检查，如果没有获取到任何有效数据，返回空数组
	if len(results) == 0 {
		logger.Infof("经过过滤后未获取到有效漏洞数据")
		return []*VulnInfo{}, nil
	}

	// 始终输出最终结果，无论是否为调试模式
	logger.Infof("成功获取新的阿里云漏洞数据，共 %d 条", len(results))
	return results, nil
}
