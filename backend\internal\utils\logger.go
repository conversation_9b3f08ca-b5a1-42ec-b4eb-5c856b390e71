package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// String 返回日志级别字符串
func (l LogLevel) String() string {
	switch l {
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelInfo:
		return "INFO"
	case LogLevelWarn:
		return "WARN"
	case LogLevelError:
		return "ERROR"
	case LogLevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger 日志记录器接口
type Logger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
}

// SimpleLogger 简单日志记录器
type SimpleLogger struct {
	logger   *log.Logger
	level    LogLevel
	showFile bool
}

// NewSimpleLogger 创建简单日志记录器
func NewSimpleLogger(output io.Writer, level LogLevel, showFile bool) *SimpleLogger {
	return &SimpleLogger{
		logger:   log.New(output, "", 0),
		level:    level,
		showFile: showFile,
	}
}

// NewFileLogger 创建文件日志记录器
func NewFileLogger(filename string, level LogLevel, showFile bool) (*SimpleLogger, error) {
	// 确保日志目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}
	
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil, fmt.Errorf("打开日志文件失败: %w", err)
	}
	
	return NewSimpleLogger(file, level, showFile), nil
}

// NewConsoleLogger 创建控制台日志记录器
func NewConsoleLogger(level LogLevel, showFile bool) *SimpleLogger {
	return NewSimpleLogger(os.Stdout, level, showFile)
}

// formatMessage 格式化日志消息
func (sl *SimpleLogger) formatMessage(level LogLevel, msg string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	
	var fileInfo string
	if sl.showFile {
		_, file, line, ok := runtime.Caller(3)
		if ok {
			file = filepath.Base(file)
			fileInfo = fmt.Sprintf(" [%s:%d]", file, line)
		}
	}
	
	return fmt.Sprintf("[%s] %s%s %s", timestamp, level.String(), fileInfo, msg)
}

// shouldLog 检查是否应该记录日志
func (sl *SimpleLogger) shouldLog(level LogLevel) bool {
	return level >= sl.level
}

// log 记录日志
func (sl *SimpleLogger) log(level LogLevel, args ...interface{}) {
	if !sl.shouldLog(level) {
		return
	}
	
	msg := fmt.Sprint(args...)
	formatted := sl.formatMessage(level, msg)
	sl.logger.Println(formatted)
	
	if level == LogLevelFatal {
		os.Exit(1)
	}
}

// logf 格式化记录日志
func (sl *SimpleLogger) logf(level LogLevel, format string, args ...interface{}) {
	if !sl.shouldLog(level) {
		return
	}
	
	msg := fmt.Sprintf(format, args...)
	formatted := sl.formatMessage(level, msg)
	sl.logger.Println(formatted)
	
	if level == LogLevelFatal {
		os.Exit(1)
	}
}

// Debug 记录调试日志
func (sl *SimpleLogger) Debug(args ...interface{}) {
	sl.log(LogLevelDebug, args...)
}

// Debugf 格式化记录调试日志
func (sl *SimpleLogger) Debugf(format string, args ...interface{}) {
	sl.logf(LogLevelDebug, format, args...)
}

// Info 记录信息日志
func (sl *SimpleLogger) Info(args ...interface{}) {
	sl.log(LogLevelInfo, args...)
}

// Infof 格式化记录信息日志
func (sl *SimpleLogger) Infof(format string, args ...interface{}) {
	sl.logf(LogLevelInfo, format, args...)
}

// Warn 记录警告日志
func (sl *SimpleLogger) Warn(args ...interface{}) {
	sl.log(LogLevelWarn, args...)
}

// Warnf 格式化记录警告日志
func (sl *SimpleLogger) Warnf(format string, args ...interface{}) {
	sl.logf(LogLevelWarn, format, args...)
}

// Error 记录错误日志
func (sl *SimpleLogger) Error(args ...interface{}) {
	sl.log(LogLevelError, args...)
}

// Errorf 格式化记录错误日志
func (sl *SimpleLogger) Errorf(format string, args ...interface{}) {
	sl.logf(LogLevelError, format, args...)
}

// Fatal 记录致命错误日志并退出程序
func (sl *SimpleLogger) Fatal(args ...interface{}) {
	sl.log(LogLevelFatal, args...)
}

// Fatalf 格式化记录致命错误日志并退出程序
func (sl *SimpleLogger) Fatalf(format string, args ...interface{}) {
	sl.logf(LogLevelFatal, format, args...)
}

// MultiLogger 多输出日志记录器
type MultiLogger struct {
	loggers []Logger
}

// NewMultiLogger 创建多输出日志记录器
func NewMultiLogger(loggers ...Logger) *MultiLogger {
	return &MultiLogger{
		loggers: loggers,
	}
}

// AddLogger 添加日志记录器
func (ml *MultiLogger) AddLogger(logger Logger) {
	ml.loggers = append(ml.loggers, logger)
}

// Debug 记录调试日志
func (ml *MultiLogger) Debug(args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Debug(args...)
	}
}

// Debugf 格式化记录调试日志
func (ml *MultiLogger) Debugf(format string, args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Debugf(format, args...)
	}
}

// Info 记录信息日志
func (ml *MultiLogger) Info(args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Info(args...)
	}
}

// Infof 格式化记录信息日志
func (ml *MultiLogger) Infof(format string, args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Infof(format, args...)
	}
}

// Warn 记录警告日志
func (ml *MultiLogger) Warn(args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Warn(args...)
	}
}

// Warnf 格式化记录警告日志
func (ml *MultiLogger) Warnf(format string, args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Warnf(format, args...)
	}
}

// Error 记录错误日志
func (ml *MultiLogger) Error(args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Error(args...)
	}
}

// Errorf 格式化记录错误日志
func (ml *MultiLogger) Errorf(format string, args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Errorf(format, args...)
	}
}

// Fatal 记录致命错误日志并退出程序
func (ml *MultiLogger) Fatal(args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Fatal(args...)
	}
}

// Fatalf 格式化记录致命错误日志并退出程序
func (ml *MultiLogger) Fatalf(format string, args ...interface{}) {
	for _, logger := range ml.loggers {
		logger.Fatalf(format, args...)
	}
}

// 全局日志记录器
var defaultLogger Logger = NewConsoleLogger(LogLevelInfo, true)

// SetDefaultLogger 设置默认日志记录器
func SetDefaultLogger(logger Logger) {
	defaultLogger = logger
}

// GetDefaultLogger 获取默认日志记录器
func GetDefaultLogger() Logger {
	return defaultLogger
}

// 全局日志函数

// Debug 记录调试日志
func Debug(args ...interface{}) {
	defaultLogger.Debug(args...)
}

// Debugf 格式化记录调试日志
func Debugf(format string, args ...interface{}) {
	defaultLogger.Debugf(format, args...)
}

// Info 记录信息日志
func Info(args ...interface{}) {
	defaultLogger.Info(args...)
}

// Infof 格式化记录信息日志
func Infof(format string, args ...interface{}) {
	defaultLogger.Infof(format, args...)
}

// Warn 记录警告日志
func Warn(args ...interface{}) {
	defaultLogger.Warn(args...)
}

// Warnf 格式化记录警告日志
func Warnf(format string, args ...interface{}) {
	defaultLogger.Warnf(format, args...)
}

// Error 记录错误日志
func Error(args ...interface{}) {
	defaultLogger.Error(args...)
}

// Errorf 格式化记录错误日志
func Errorf(format string, args ...interface{}) {
	defaultLogger.Errorf(format, args...)
}

// Fatal 记录致命错误日志并退出程序
func Fatal(args ...interface{}) {
	defaultLogger.Fatal(args...)
}

// Fatalf 格式化记录致命错误日志并退出程序
func Fatalf(format string, args ...interface{}) {
	defaultLogger.Fatalf(format, args...)
}

// ParseLogLevel 解析日志级别字符串
func ParseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return LogLevelDebug
	case "INFO":
		return LogLevelInfo
	case "WARN", "WARNING":
		return LogLevelWarn
	case "ERROR":
		return LogLevelError
	case "FATAL":
		return LogLevelFatal
	default:
		return LogLevelInfo
	}
}
