# 生产策略配置 - 数据质量配置改为定时任务配置

## 修改概述

将生产策略配置中的"数据质量配置"部分改为"定时任务配置"，支持开启禁用及间隔设置，同步修改前后端及数据库。

## 修改内容

### 1. 数据库模型修改 (backend/internal/models/ioc.go)

**移除的字段：**
- `MinDataPoints` - 最小数据点数量
- `RequireIOC` - 是否要求IOC
- `RequireCategory` - 是否要求攻击类别

**新增的字段：**
- `ScheduleEnabled` - 是否启用定时任务 (bool, 默认false)
- `ScheduleInterval` - 定时任务间隔，单位分钟 (int, 默认60)
- `LastRunTime` - 上次运行时间 (*int64, 可为null)
- `NextRunTime` - 下次运行时间 (*int64, 可为null)

### 2. 前端界面修改 (frontend/src/ProductionStrategy.vue)

**界面变更：**
- 将"数据质量配置"分组改为"定时任务配置"
- 移除最小数据点数量、要求IOC、要求攻击类别等配置项
- 新增启用定时任务开关
- 新增任务间隔设置（1-1440分钟）
- 新增上次运行时间和下次运行时间显示（只读）

**数据结构变更：**
- 移除 `minDataPoints`, `requireIOC`, `requireCategory` 字段
- 新增 `scheduleEnabled`, `scheduleInterval`, `lastRunTime`, `nextRunTime` 字段
- 更新表单验证规则

**新增功能：**
- 添加时间戳格式化函数 `formatTimestamp()`
- 条件显示：只有启用定时任务时才显示间隔设置

### 3. 后端API修改 (backend/internal/handlers/production_strategy.go)

**请求结构体更新：**
- `CreateProductionStrategyRequest` 和 `UpdateProductionStrategyRequest`
- 移除数据质量相关字段
- 新增定时任务相关字段

**默认配置更新：**
- 创建默认策略时设置 `ScheduleEnabled: false`, `ScheduleInterval: 60`

**业务逻辑调整：**
- 移除数据质量过滤逻辑
- 保留攻击次数和威胁评分过滤

### 4. 数据库迁移 (backend/internal/database/manager.go)

**新增迁移函数：**
- `migrateProductionStrategyScheduleFields()` - 处理定时任务字段迁移

**迁移内容：**
- 添加新的定时任务相关字段
- 删除旧的数据质量相关字段
- 支持增量迁移，避免重复执行

**字段映射：**
```sql
-- 新增字段
ALTER TABLE production_strategies ADD COLUMN schedule_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE production_strategies ADD COLUMN schedule_interval INT DEFAULT 60;
ALTER TABLE production_strategies ADD COLUMN last_run_time BIGINT DEFAULT NULL;
ALTER TABLE production_strategies ADD COLUMN next_run_time BIGINT DEFAULT NULL;

-- 删除旧字段
ALTER TABLE production_strategies DROP COLUMN min_data_points;
ALTER TABLE production_strategies DROP COLUMN require_ioc;
ALTER TABLE production_strategies DROP COLUMN require_category;
```

## 功能说明

### 定时任务配置
1. **启用定时任务**：控制是否开启自动生成IOC情报的定时任务
2. **任务间隔**：设置定时任务的执行间隔，支持1-1440分钟（1分钟到24小时）
3. **运行时间显示**：显示上次运行时间和下次预计运行时间（只读）

### 向后兼容性
- 数据库迁移会自动处理字段变更
- 现有数据不会丢失
- API接口保持兼容性

## 测试验证

### 编译测试
- ✅ 后端编译成功 (`go build`)
- ✅ 前端构建成功 (`npm run build`)
- ✅ 无语法错误和类型错误

### 功能测试建议
1. 启动应用，访问生产策略配置页面
2. 验证界面显示正确（定时任务配置替代数据质量配置）
3. 测试启用/禁用定时任务开关
4. 测试任务间隔设置和验证
5. 验证配置保存和加载功能
6. 检查数据库字段迁移是否正确执行

## 注意事项

1. **数据库备份**：在生产环境执行前建议备份数据库
2. **迁移顺序**：数据库迁移会在应用启动时自动执行
3. **配置兼容**：现有的生产策略配置会保留攻击过滤和时间配置部分
4. **定时任务实现**：本次修改只是配置界面，实际的定时任务调度需要另外实现

## 后续工作

1. 实现定时任务调度器
2. 根据配置的间隔自动执行IOC情报生成
3. 更新运行时间字段
4. 添加任务执行日志和状态监控
