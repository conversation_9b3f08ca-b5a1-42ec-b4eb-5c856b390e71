package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查ioc_source_data表的字段
	fmt.Println("\n📊 检查ioc_source_data表的字段:")
	
	var columns []struct {
		Field   string `json:"field"`
		Type    string `json:"type"`
		Null    string `json:"null"`
		Key     string `json:"key"`
		Default string `json:"default"`
		Extra   string `json:"extra"`
	}
	
	err = db.Raw("DESCRIBE ioc_source_data").Scan(&columns).Error
	if err != nil {
		log.Fatalf("查询表结构失败: %v", err)
	}
	
	fmt.Println("表字段:")
	for _, col := range columns {
		fmt.Printf("  %s: %s\n", col.Field, col.Type)
	}
	
	// 测试删除重置逻辑
	fmt.Println("\n🔍 测试删除重置逻辑:")
	
	// 查找一个IOC情报
	type IOCInfo struct {
		ID  uint   `json:"id"`
		IOC string `json:"ioc"`
	}
	
	var iocInfo IOCInfo
	err = db.Table("ioc_intelligence").
		Select("id, ioc").
		First(&iocInfo).Error
	
	if err != nil {
		fmt.Printf("没有找到IOC情报: %v\n", err)
		return
	}
	
	fmt.Printf("找到IOC情报: ID=%d, IOC=%s\n", iocInfo.ID, iocInfo.IOC)
	
	// 查找对应的源数据
	var sourceDataCount int64
	err = db.Table("ioc_source_data").
		Where("attack_ip = ?", iocInfo.IOC).
		Count(&sourceDataCount).Error
	
	if err != nil {
		fmt.Printf("查询源数据失败: %v\n", err)
		return
	}
	
	fmt.Printf("对应的源数据记录数: %d\n", sourceDataCount)
	
	// 查找已处理的源数据
	var processedCount int64
	err = db.Table("ioc_source_data").
		Where("attack_ip = ? AND processed_status = ?", iocInfo.IOC, "processed").
		Count(&processedCount).Error
	
	if err != nil {
		fmt.Printf("查询已处理源数据失败: %v\n", err)
		return
	}
	
	fmt.Printf("已处理的源数据记录数: %d\n", processedCount)
	
	// 模拟重置操作
	if processedCount > 0 {
		fmt.Println("\n🔄 模拟重置操作:")
		
		result := db.Table("ioc_source_data").
			Where("attack_ip = ? AND processed_status = ?", iocInfo.IOC, "processed").
			Updates(map[string]interface{}{
				"processed_status": "unprocessed",
				"processed_at":     0,
			})
		
		if result.Error != nil {
			fmt.Printf("重置失败: %v\n", result.Error)
		} else {
			fmt.Printf("✅ 成功重置 %d 条记录\n", result.RowsAffected)
		}
		
		// 恢复状态
		db.Table("ioc_source_data").
			Where("attack_ip = ? AND processed_status = ?", iocInfo.IOC, "unprocessed").
			Updates(map[string]interface{}{
				"processed_status": "processed",
				"processed_at":     1732464000,
			})
		fmt.Println("已恢复原状态")
	} else {
		fmt.Println("没有已处理的源数据需要重置")
	}
}
