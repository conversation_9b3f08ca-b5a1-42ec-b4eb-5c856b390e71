package handlers

import (
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"vulnerability_push/internal/models"
	"gorm.io/gorm"
)

// UUIDDedupConfig UUID去重配置
type UUIDDedupConfig struct {
	BatchSize    int // 批量查询大小，默认1000
	WorkerCount  int // 工作协程数量，默认4
	SaveBatchSize int // 保存批次大小，默认1000
}

// DefaultUUIDDedupConfig 默认UUID去重配置
func DefaultUUIDDedupConfig() *UUIDDedupConfig {
	return &UUIDDedupConfig{
		BatchSize:     1000,
		WorkerCount:   4,
		SaveBatchSize: 1000,
	}
}

// BaseDataInterface 数据接口基类
// 提供通用的数据处理功能
type BaseDataInterface struct {
	handler      *DataInterfaceHandler
	db           *gorm.DB
	dedupConfig  *UUIDDedupConfig
}

// NewBaseDataInterface 创建数据接口基类
func NewBaseDataInterface(handler *DataInterfaceHandler) *BaseDataInterface {
	return &BaseDataInterface{
		handler:     handler,
		db:          handler.db,
		dedupConfig: DefaultUUIDDedupConfig(),
	}
}

// SetDedupConfig 设置UUID去重配置
func (b *BaseDataInterface) SetDedupConfig(config *UUIDDedupConfig) {
	if config != nil {
		b.dedupConfig = config
	}
}

// SetDedupConfigByName 通过配置名称设置UUID去重配置
func (b *BaseDataInterface) SetDedupConfigByName(configName string) {
	config := GlobalUUIDDedupConfigManager.GetConfig(configName)
	b.SetDedupConfig(config)
}

// OptimizeDedupConfigForDataSize 根据数据大小优化去重配置
func (b *BaseDataInterface) OptimizeDedupConfigForDataSize(dataSize int) {
	optimized := GlobalUUIDDedupConfigManager.OptimizeConfigForDataSize(b.dedupConfig, dataSize)
	b.SetDedupConfig(optimized)
}

// IsInternalIP 检查IP是否为内网地址
func (b *BaseDataInterface) IsInternalIP(ip string) bool {
	if ip == "" {
		return false
	}
	
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}
	
	// 检查是否为私有IP地址
	return parsedIP.IsPrivate() || parsedIP.IsLoopback() || parsedIP.IsLinkLocalUnicast()
}

// IsInternalTraffic 检查是否为内网流量
func (b *BaseDataInterface) IsInternalTraffic(srcIP, dstIP string) bool {
	return b.IsInternalIP(srcIP) && b.IsInternalIP(dstIP)
}

// IsWhitelistedIP 检查IP是否在白名单中
func (b *BaseDataInterface) IsWhitelistedIP(ip string) bool {
	var count int64
	err := b.db.Model(&models.IOCWhitelist{}).
		Where("ioc = ?", ip).
		Count(&count).Error

	if err != nil {
		fmt.Printf("查询白名单失败: %v\n", err)
		return false
	}

	return count > 0
}

// ShouldIncludeAttackFlow 检查是否应该包含此攻击流
func (b *BaseDataInterface) ShouldIncludeAttackFlow(srcIP, dstIP string, targetOrganizations []string) bool {
	// 如果没有指定目标组织，则包含所有外网流量
	if len(targetOrganizations) == 0 {
		return !b.IsInternalTraffic(srcIP, dstIP)
	}
	
	// 检查是否涉及目标组织的IP段
	for _, org := range targetOrganizations {
		if b.isIPInOrganization(srcIP, org) || b.isIPInOrganization(dstIP, org) {
			return true
		}
	}
	
	return false
}

// isIPInOrganization 检查IP是否属于指定组织
func (b *BaseDataInterface) isIPInOrganization(ip, organization string) bool {
	// 这里可以实现更复杂的组织IP段匹配逻辑
	// 目前简单实现为字符串包含检查
	return strings.Contains(ip, organization)
}

// SaveProcessedUUID 保存已处理的UUID
func (b *BaseDataInterface) SaveProcessedUUID(uuid, sourceType string) error {
	processedUUID := models.ProcessedUUID{
		UUID:        uuid,
		SourceType:  sourceType,
		ProcessedAt: time.Now(), // 设置处理时间
	}

	return b.db.Create(&processedUUID).Error
}

// IsUUIDProcessed 检查UUID是否已处理
func (b *BaseDataInterface) IsUUIDProcessed(uuid, sourceType string) bool {
	var count int64
	err := b.db.Model(&models.ProcessedUUID{}).
		Where("uuid = ? AND source_type = ?", uuid, sourceType).
		Count(&count).Error

	if err != nil {
		fmt.Printf("查询UUID失败: %v\n", err)
		return false
	}

	return count > 0
}

// BatchCheckUUIDsProcessed 批量检查UUID是否已处理（优化版本）
func (b *BaseDataInterface) BatchCheckUUIDsProcessed(uuids []string, sourceType string) (map[string]bool, error) {
	if len(uuids) == 0 {
		return make(map[string]bool), nil
	}

	result := make(map[string]bool)

	// 初始化所有UUID为未处理状态
	for _, uuid := range uuids {
		result[uuid] = false
	}

	// 批量查询已处理的UUID
	var processedUUIDs []models.ProcessedUUID
	err := b.db.Model(&models.ProcessedUUID{}).
		Where("uuid IN ? AND source_type = ?", uuids, sourceType).
		Select("uuid").
		Find(&processedUUIDs).Error

	if err != nil {
		return nil, fmt.Errorf("批量查询UUID失败: %v", err)
	}

	// 标记已处理的UUID
	for _, processed := range processedUUIDs {
		result[processed.UUID] = true
	}

	return result, nil
}

// ConcurrentBatchCheckUUIDsProcessed 并发批量检查UUID是否已处理（多线程优化版本）
func (b *BaseDataInterface) ConcurrentBatchCheckUUIDsProcessed(uuids []string, sourceType string, batchSize int, workerCount int) (map[string]bool, error) {
	if len(uuids) == 0 {
		return make(map[string]bool), nil
	}

	// 使用配置参数或传入参数
	if batchSize <= 0 {
		batchSize = b.dedupConfig.BatchSize
	}
	if workerCount <= 0 {
		workerCount = b.dedupConfig.WorkerCount
	}

	result := make(map[string]bool)
	resultMutex := sync.RWMutex{}

	// 初始化所有UUID为未处理状态
	for _, uuid := range uuids {
		result[uuid] = false
	}

	// 创建工作任务通道
	jobs := make(chan []string, workerCount*2)
	errors := make(chan error, workerCount)
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for batch := range jobs {
				fmt.Printf("Worker %d 处理 %d 个UUID\n", workerID, len(batch))

				// 批量查询当前批次的UUID
				var processedUUIDs []models.ProcessedUUID
				err := b.db.Model(&models.ProcessedUUID{}).
					Where("uuid IN ? AND source_type = ?", batch, sourceType).
					Select("uuid").
					Find(&processedUUIDs).Error

				if err != nil {
					errors <- fmt.Errorf("Worker %d 查询失败: %v", workerID, err)
					return
				}

				// 更新结果（需要加锁）
				resultMutex.Lock()
				for _, processed := range processedUUIDs {
					result[processed.UUID] = true
				}
				resultMutex.Unlock()
			}
		}(i)
	}

	// 分批发送任务
	go func() {
		defer close(jobs)

		for i := 0; i < len(uuids); i += batchSize {
			end := i + batchSize
			if end > len(uuids) {
				end = len(uuids)
			}

			batch := make([]string, end-i)
			copy(batch, uuids[i:end])
			jobs <- batch
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()
	close(errors)

	// 检查是否有错误
	for err := range errors {
		return nil, err
	}

	fmt.Printf("并发UUID去重完成 - 总数: %d, 工作协程: %d, 批次大小: %d\n",
		len(uuids), workerCount, batchSize)

	return result, nil
}

// OptimizedBatchCheckUUIDsProcessed 优化的批量UUID检查（使用默认配置）
func (b *BaseDataInterface) OptimizedBatchCheckUUIDsProcessed(uuids []string, sourceType string) (map[string]bool, error) {
	// 根据数据量选择最优策略
	if len(uuids) < 100 {
		// 小数据量使用简单批量查询
		return b.BatchCheckUUIDsProcessed(uuids, sourceType)
	} else {
		// 大数据量使用并发批量查询
		return b.ConcurrentBatchCheckUUIDsProcessed(uuids, sourceType, 0, 0)
	}
}

// BatchSaveProcessedUUIDs 批量保存已处理的UUID（优化版本）
func (b *BaseDataInterface) BatchSaveProcessedUUIDs(uuids []string, sourceType string, batchSize int) error {
	if len(uuids) == 0 {
		return nil
	}

	// 设置默认批次大小
	if batchSize <= 0 {
		batchSize = 1000
	}

	fmt.Printf("开始批量保存UUID - 总数: %d, 批次大小: %d\n", len(uuids), batchSize)

	// 分批处理
	savedCount := 0
	for i := 0; i < len(uuids); i += batchSize {
		end := i + batchSize
		if end > len(uuids) {
			end = len(uuids)
		}

		batch := uuids[i:end]

		// 准备批量插入数据
		var processedUUIDs []models.ProcessedUUID
		now := time.Now()

		for _, uuid := range batch {
			if uuid != "" {
				processedUUIDs = append(processedUUIDs, models.ProcessedUUID{
					UUID:        uuid,
					SourceType:  sourceType,
					ProcessedAt: now,
				})
			}
		}

		if len(processedUUIDs) > 0 {
			// 使用批量插入，忽略重复键错误
			err := b.db.CreateInBatches(processedUUIDs, len(processedUUIDs)).Error
			if err != nil {
				fmt.Printf("批量保存UUID失败 (批次 %d-%d): %v\n", i+1, end, err)
				// 继续处理下一批，不中断整个过程
				continue
			}
			savedCount += len(processedUUIDs)
		}

		fmt.Printf("已保存批次 %d-%d (%d个UUID)\n", i+1, end, len(processedUUIDs))
	}

	fmt.Printf("批量保存UUID完成 - 成功保存: %d/%d\n", savedCount, len(uuids))
	return nil
}

// BatchSaveIOCSourceData 批量保存IOC源数据
func (b *BaseDataInterface) BatchSaveIOCSourceData(sourceDataList []models.IOCIntelligenceData) error {
	if len(sourceDataList) == 0 {
		return nil
	}
	
	// 使用批量插入提高性能
	batchSize := 100
	for i := 0; i < len(sourceDataList); i += batchSize {
		end := i + batchSize
		if end > len(sourceDataList) {
			end = len(sourceDataList)
		}
		
		batch := sourceDataList[i:end]
		if err := b.db.CreateInBatches(batch, len(batch)).Error; err != nil {
			return fmt.Errorf("批量保存IOC源数据失败: %v", err)
		}
	}
	
	return nil
}

// GetAttackCategoryName 获取攻击类别名称
func (b *BaseDataInterface) GetAttackCategoryName(categoryCode string) string {
	categoryMap := map[string]string{
		"20404": "漏洞利用攻击",
		"20402": "Web攻击",
		"20401": "暴力破解",
		"20403": "恶意软件",
		"20405": "网络扫描",
		"20406": "拒绝服务攻击",
		"20407": "数据泄露",
		"20408": "钓鱼攻击",
		"20409": "僵尸网络",
		"20410": "APT攻击",
	}
	
	if name, exists := categoryMap[categoryCode]; exists {
		return name
	}
	
	return categoryCode // 如果没有映射，返回原始代码
}

// ValidateIPAddress 验证IP地址格式
func (b *BaseDataInterface) ValidateIPAddress(ip string) bool {
	return net.ParseIP(ip) != nil
}

// NormalizeIP 标准化IP地址
func (b *BaseDataInterface) NormalizeIP(ip string) string {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return ip
	}
	return parsedIP.String()
}

// GetExternalIP 从攻击流中获取外网IP
func (b *BaseDataInterface) GetExternalIP(srcIP, dstIP string) string {
	if !b.IsInternalIP(srcIP) {
		return srcIP
	}
	if !b.IsInternalIP(dstIP) {
		return dstIP
	}
	return "" // 都是内网IP
}

// CalculateRiskScore 计算风险评分
func (b *BaseDataInterface) CalculateRiskScore(severity string, attackCount int) int {
	baseScore := 0
	
	// 根据严重程度设置基础分数
	switch strings.ToLower(severity) {
	case "critical", "高危":
		baseScore = 80
	case "high", "中危":
		baseScore = 60
	case "medium", "低危":
		baseScore = 40
	case "low", "信息":
		baseScore = 20
	default:
		baseScore = 30
	}
	
	// 根据攻击次数调整分数
	if attackCount > 100 {
		baseScore += 20
	} else if attackCount > 50 {
		baseScore += 15
	} else if attackCount > 10 {
		baseScore += 10
	} else if attackCount > 1 {
		baseScore += 5
	}
	
	// 确保分数在0-100范围内
	if baseScore > 100 {
		baseScore = 100
	}
	
	return baseScore
}

// UUIDDedupPerformanceStats UUID去重性能统计
type UUIDDedupPerformanceStats struct {
	TotalUUIDs       int           `json:"total_uuids"`
	ProcessedUUIDs   int           `json:"processed_uuids"`
	UnprocessedUUIDs int           `json:"unprocessed_uuids"`
	QueryDuration    time.Duration `json:"query_duration"`
	SaveDuration     time.Duration `json:"save_duration"`
	TotalDuration    time.Duration `json:"total_duration"`
	Method           string        `json:"method"` // "single", "batch", "concurrent"
	BatchSize        int           `json:"batch_size"`
	WorkerCount      int           `json:"worker_count"`
}

// MeasuredOptimizedBatchCheckUUIDsProcessed 带性能测量的优化批量UUID检查
func (b *BaseDataInterface) MeasuredOptimizedBatchCheckUUIDsProcessed(uuids []string, sourceType string) (map[string]bool, *UUIDDedupPerformanceStats, error) {
	startTime := time.Now()

	stats := &UUIDDedupPerformanceStats{
		TotalUUIDs:  len(uuids),
		BatchSize:   b.dedupConfig.BatchSize,
		WorkerCount: b.dedupConfig.WorkerCount,
	}

	// 选择处理方法
	var result map[string]bool
	var err error

	if len(uuids) < 100 {
		stats.Method = "batch"
		queryStart := time.Now()
		result, err = b.BatchCheckUUIDsProcessed(uuids, sourceType)
		stats.QueryDuration = time.Since(queryStart)
	} else {
		stats.Method = "concurrent"
		queryStart := time.Now()
		result, err = b.ConcurrentBatchCheckUUIDsProcessed(uuids, sourceType, 0, 0)
		stats.QueryDuration = time.Since(queryStart)
	}

	if err != nil {
		return nil, stats, err
	}

	// 统计结果
	processedCount := 0
	for _, processed := range result {
		if processed {
			processedCount++
		}
	}

	stats.ProcessedUUIDs = processedCount
	stats.UnprocessedUUIDs = len(uuids) - processedCount
	stats.TotalDuration = time.Since(startTime)

	fmt.Printf("UUID去重性能统计 - 方法: %s, 总数: %d, 已处理: %d, 未处理: %d, 查询耗时: %v, 总耗时: %v\n",
		stats.Method, stats.TotalUUIDs, stats.ProcessedUUIDs, stats.UnprocessedUUIDs,
		stats.QueryDuration, stats.TotalDuration)

	return result, stats, nil
}
