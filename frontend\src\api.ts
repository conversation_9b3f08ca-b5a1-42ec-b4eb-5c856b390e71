import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'
import router from './router'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.DEV ? '/api' : 'http://localhost:55555/api',  // 开发模式使用代理，生产模式直接指向后端
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`
    
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 创建一个带有重试逻辑的请求函数
const requestWithRetry = async <T>(config: AxiosRequestConfig, retries = 2, delay = 1000): Promise<T> => {
  try {
    const response = await api(config)
    return response.data
  } catch (error: any) {
    // 如果没有重试次数了或者错误不是网络错误/超时，直接抛出
    if (retries <= 0 || (error.response && error.response.status !== 0)) {
      throw error
    }
    
    // 网络错误或超时时进行重试
    console.log(`请求失败，将在${delay}ms后重试，剩余重试次数: ${retries}`)
    await new Promise(resolve => setTimeout(resolve, delay))
    return requestWithRetry<T>(config, retries - 1, delay * 2)
  }
}

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 网络错误处理
    if (!error.response) {
      console.error('网络错误:', error.message)
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    // 获取错误信息
    const errorMsg = error.response.data?.msg || '请求失败'
    const status = error.response.status
    const url = error.config?.url || ''
    
    // 根据状态码处理不同类型的错误
    switch (status) {
      case 401:
        // 登录接口的401错误单独处理
        if (url.includes('/api/login')) {
          // 直接显示后端返回的错误信息，不清除token和跳转
          ElMessage.error(errorMsg)
        } else {
          // 其他接口的401错误，清除token并跳转到登录页
          localStorage.removeItem('token')
          // 只有当当前不在登录页时，才显示登录过期提示
          if (window.location.pathname !== '/') {
            ElMessage.error('登录已过期，请重新登录')
          }
          router.push('/')
        }
        break
        
      case 403:
        // 检查是否是敏感词错误
        if (error.response.data?.data?.sensitive_words) {
          // 敏感词错误不在这里处理，让具体的组件处理
          // 这样可以避免重复显示错误信息
        } else {
          ElMessage.error('没有操作权限')
        }
        break
        
      case 404:
        // 对于采集器类型列表API，不显示错误提示，让前端使用默认值
        if (!url.includes('/api/crawler/providers')) {
          ElMessage.error('请求的资源不存在')
        } else {
          console.error('获取采集器类型列表失败:', errorMsg)
        }
        break
        
      case 400:
        ElMessage.error(`请求参数错误: ${errorMsg}`)
        break
        
      case 500:
        ElMessage.error(`服务器错误: ${errorMsg}`)
        break
        
      default:
        // 对于采集器类型列表API，不显示错误提示
        if (!url.includes('/api/crawler/providers')) {
          ElMessage.error(errorMsg)
        } else {
          console.error('获取采集器类型列表失败:', errorMsg)
        }
    }
    
    return Promise.reject(error)
  }
)

// 用户类型定义
export interface User {
  id?: number
  username: string
  email?: string
  role?: string
  status?: boolean
  apiKey?: string
  createdAt?: number
  updatedAt?: number
}

// 创建用户请求类型
export interface CreateUserRequest {
  username: string
  password: string
  email: string
  role?: string
}

// 更新用户请求类型
export interface UpdateUserRequest {
  email: string
  role: string
  status: boolean
}

// 漏洞类型定义
export interface Vulnerability {
  id?: number
  name: string
  vulnId: string
  severity: string
  tags: string[]
  disclosureDate?: string
  pushReason?: string
  source?: string
  description?: string
  references?: string[]
  remediation?: string
  createdAt?: number
}

// 获取漏洞列表请求参数
export interface GetVulnerabilitiesParams {
  page: number
  pageSize: number
  keyword?: string
  severity?: string
  source?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: string
}

// 创建漏洞请求类型
export interface CreateVulnerabilityRequest {
  name: string
  vulnId: string
  severity: string
  tags?: string[]
  disclosureDate?: string
  pushReason?: string
  source?: string
  description?: string
  references?: string[]
  remediation?: string
}

// 更新漏洞请求类型
export interface UpdateVulnerabilityRequest {
  name?: string
  vulnId?: string
  severity?: string
  tags?: string[]
  disclosureDate?: string
  pushReason?: string
  source?: string
  description?: string
  references?: string[]
  remediation?: string
}

// 推送通道类型定义
export interface PushChannel {
  id?: number
  name: string
  type: string
  description?: string
  config?: {
    webhook_url?: string
    access_token?: string
    secret?: string
  }
  status: boolean
  createdAt?: number
  updatedAt?: number
}

// 推送记录类型定义
export interface PushRecord {
  id?: number
  vulnerabilityID: number
  channelID: number
  channelName: string
  channelType: string
  status: string
  errorMessage?: string
  pushedAt: number
}

// 推送策略类型定义
export interface PushPolicy {
  id?: number
  name: string
  description?: string
  channelIDs: string
  isDefault: boolean
  channelInfo?: any[]
  createdAt?: number
  updatedAt?: number
}

// 采集源类型定义
export interface Crawler {
  id?: number
  name: string
  type?: string       // 主采集源类型（向后兼容）
  types?: string[]    // 多个采集源类型
  description?: string
  config?: any
  status: boolean
  interval?: string  // 采集间隔：优先使用此字段
  schedule?: string  // 兼容旧数据
  lastRunAt?: number
  createdAt?: number
  updatedAt?: number
}

// 采集日志类型定义
export interface CrawlerLog {
  id?: number
  crawlerID: number
  status: string
  message?: string
  count: number
  startTime: number
  endTime: number
  createdAt?: number
}

// IOC情报类型定义
export interface IOCIntelligence {
  id?: number
  ioc: string
  iocType: string
  location?: string
  type: string
  riskLevel: string
  hitCount?: number
  targetOrg?: string
  description?: string
  source: string
  discoveryDate?: string
  tags?: string
  pushReason?: string
  createdAt?: number
  updatedAt?: number
}

// IOC情报推送记录类型定义
export interface IOCIntelligenceRecord {
  id?: number
  iocIntelligenceId: number
  channelId: number
  channelName: string
  channelType: string
  status: string
  errorMessage?: string
  pushedAt: number
  iocIntelligence?: IOCIntelligence
  channel?: PushChannel
}

// IOC白名单类型定义
export interface IOCWhitelist {
  id?: number
  ioc: string
  iocType: string
  remark?: string
  createdBy: string
  createdAt?: number
  updatedAt?: number
}

// IOC情报统计信息类型定义
export interface IOCIntelligenceStats {
  total: number
  iocTypes: Record<string, number>
  riskLevels: Record<string, number>
  types: Record<string, number>
  sources: Record<string, number>
}

// API接口
export default {
  // 登录
  login(data: { username: string; password: string }) {
    return api.post('/login', data)
  },

  // 验证令牌
  verifyToken() {
    return api.get('/verify')
  },

  // 获取当前用户信息
  getCurrentUser() {
    return api.get('/user')
  },

  // 退出登录
  logout() {
    // 先清除 token
    localStorage.removeItem('token')
    
    // 使用 router.replace 替代 router.push，避免添加历史记录
    router.replace('/')
    
    // 发送注销请求
    return requestWithRetry({
      method: 'post',
      url: '/logout'
    }).catch(error => {
      console.error('注销请求失败:', error)
      // 即使注销请求失败，也要确保跳转到登录页
      router.replace('/')
    })
  },

  // 获取所有用户列表
  getUsers(params?: { page?: number; pageSize?: number; username?: string; role?: string; status?: boolean }) {
    return api.get('/admin/users', { params })
  },
  
  // 获取用户详情
  getUserDetail(id: number) {
    return api.get(`/admin/users/${id}`)
  },

  // 创建用户
  createUser(data: CreateUserRequest) {
    return api.post('/admin/users', data)
  },

  // 更新用户信息
  updateUser(id: number, data: UpdateUserRequest) {
    return api.put(`/admin/users/${id}`, data)
  },

  // 删除用户
  deleteUser(id: number) {
    return api.delete(`/admin/users/${id}`)
  },

  // 重置API密钥
  resetApiKey(id: number) {
    return api.patch(`/admin/users/${id}/reset-api-key`)
  },

  // 修改自己的密码
  changePassword(data: { oldPassword: string; newPassword: string }) {
    return api.post('/change-password', data)
  },

  // 管理员修改用户密码
  adminChangePassword(id: number, data: { newPassword: string }) {
    return api.post(`/admin/users/${id}/change-password`, data)
  },

  // 验证API密钥
  validateApiKey(apiKey: string) {
    return api.post('/validate-api-key', { apiKey })
  },

  // 漏洞相关API
  // 获取漏洞列表
  getVulnerabilities(params: GetVulnerabilitiesParams) {
    return api.get('/vulnerabilities', { params })
  },

  // 获取漏洞详情
  getVulnerabilityDetail(id: number) {
    return api.get(`/vulnerabilities/${id}`)
  },

  // 获取漏洞统计信息
  getStats() {
    return api.get('/vulnerability/stats')
  },

  // 创建漏洞
  createVulnerability(data: CreateVulnerabilityRequest) {
    return api.post('/admin/vulnerabilities', data)
  },

  // 更新漏洞
  updateVulnerability(id: number, data: UpdateVulnerabilityRequest) {
    return api.put(`/admin/vulnerabilities/${id}`, data)
  },

  // 删除漏洞
  deleteVulnerability(id: number) {
    return api.delete(`/admin/vulnerabilities/${id}`)
  },

  // 批量删除漏洞
  batchDeleteVulnerabilities(ids: number[]) {
    return api.delete('/vulnerabilities/batch', { data: { ids } })
  },

  // 导出漏洞为Excel
  exportVulnerabilities(ids: number[]) {
    return api.post('/vulnerabilities/export', { ids }, { responseType: 'blob' })
  },

  // 根据时间范围和条件导出漏洞
  exportVulnerabilitiesByDate(params: { startDate?: string; endDate?: string; severities?: string[] }) {
    return api.post('/vulnerabilities/export-by-date', params)
  },
  
  // 保存自动导出配置
  saveExportConfig(data: { frequency: string; severities: string[]; weekDay?: number; monthDay?: number }) {
    return api.post('/admin/export/config', data)
  },
  
  // 获取自动导出配置
  getExportConfig() {
    return api.get('/admin/export/config')
  },
  
  // 获取导出文件列表
  getExportFiles() {
    return api.get('/admin/export/files')
  },
  
  // 下载指定的导出文件
  downloadExportFile(filename: string) {
    return api.get(`/admin/export/files/${filename}`, { responseType: 'blob' })
  },

  // 删除指定的导出文件
  deleteExportFile(filename: string) {
    return api.delete(`/admin/export/files/${filename}`)
  },

  // 推送通道相关API
  // 获取推送通道列表
  getPushChannels(params?: { page?: number; pageSize?: number }) {
    return api.get('/admin/push/channels', { params })
  },

  // 获取推送通道详情
  getPushChannel(id: number) {
    return api.get(`/admin/push/channels/${id}`)
  },

  // 创建推送通道
  createPushChannel(data: PushChannel) {
    return api.post('/admin/push/channels', data)
  },

  // 更新推送通道
  updatePushChannel(id: number, data: PushChannel) {
    return api.put(`/admin/push/channels/${id}`, data)
  },

  // 删除推送通道
  deletePushChannel(id: number) {
    return api.delete(`/admin/push/channels/${id}`)
  },

  // 测试推送通道
  testPushChannel(id: number) {
    return api.post(`/admin/push/channels/${id}/test`)
  },

  // 推送漏洞
  pushVulnerability(id: number, channelId?: number, policyId?: number) {
    const params: any = {}
    if (channelId) params.channel_id = channelId
    if (policyId) params.policy_id = policyId

    return api.post(`/admin/vulnerabilities/${id}/push`, null, { params })
  },

  // 使用默认策略推送漏洞
  pushVulnerabilityUsingDefaultPolicy(id: number) {
    return this.pushVulnerability(id)
  },

  // 使用指定策略推送漏洞
  pushVulnerabilityUsingPolicy(id: number, policyId: number) {
    return this.pushVulnerability(id, undefined, policyId)
  },

  // 获取推送记录
  getPushRecords(params?: { keyword?: string, start_time?: string, end_time?: string, page?: number, pageSize?: number }) {
    return api.get('/admin/push/records', { params })
  },

  // 删除推送记录
  deletePushRecord(id: number) {
    return api.delete(`/admin/push/records/${id}`)
  },

  // 推送策略相关API
  // 获取推送策略列表
  getPushPolicies(params?: { page?: number; pageSize?: number }) {
    return api.get('/admin/push/policies', { params })
  },

  // 获取推送策略详情
  getPushPolicy(id: number) {
    return api.get(`/admin/push/policies/${id}`)
  },

  // 创建推送策略
  createPushPolicy(data: PushPolicy) {
    return api.post('/admin/push/policies', data)
  },

  // 更新推送策略
  updatePushPolicy(id: number, data: PushPolicy) {
    return api.put(`/admin/push/policies/${id}`, data)
  },

  // 删除推送策略
  deletePushPolicy(id: number) {
    return api.delete(`/admin/push/policies/${id}`)
  },

  // 推送白名单相关
  getPushWhitelist() {
    return api.get('/admin/push/whitelist')
  },

  updatePushWhitelist(data: {
    keywords: string;
    autoPush: boolean;
    policyId: number;
    description: string;
  }) {
    return api.put('/admin/push/whitelist', data)
  },

  // RSS配置相关
  getRssConfig() {
    return api.get('/admin/push/rss/config')
  },

  updateRssConfig(data: {
    enabled: boolean;
    requireAuth: boolean;
    title: string;
    description: string;
    itemCount: number;
    includeSeverity: string;
    excludeTags: string;
  }) {
    return api.put('/admin/push/rss/config', data)
  },

  // 获取RSS订阅URL
  getRssUrl(apiKey?: string) {
    const baseUrl = `${window.location.protocol}//${window.location.host}/api/rss`
    if (apiKey) {
      return `${baseUrl}?key=${apiKey}`
    }
    return baseUrl
  },

  // 采集源相关API
  // 获取采集源列表
  getCrawlers() {
    return api.get('/admin/crawlers')
  },
  
  // 获取采集源详情
  getCrawlerDetail(id: number) {
    return api.get(`/admin/crawlers/${id}`)
  },
  
  // 创建采集源
  createCrawler(data: Crawler) {
    // 处理多类型采集器
    const requestData = { ...data }
    if (Array.isArray(data.types) && data.types.length > 0) {
      requestData.type = data.types[0] // 主类型
    }
    return api.post('/admin/crawlers', requestData);
  },
  
  // 更新采集源
  updateCrawler(id: number, data: Crawler) {
    // 处理多类型采集器
    const requestData = { ...data }
    if (Array.isArray(data.types) && data.types.length > 0) {
      requestData.type = data.types[0] // 主类型
    }
    return api.put(`/admin/crawlers/${id}`, requestData)
  },
  
  // 删除采集源
  deleteCrawler(id: number) {
    return api.delete(`/admin/crawlers/${id}`)
  },
  
  // 手动运行采集源
  runCrawler(id: number) {
    return api.post(`/admin/crawlers/${id}/run`, {
      // 不传递pageLimit，让后端从采集器配置中读取
      startDate: '',
      endDate: ''
    })
  },
  
  // 获取采集日志
  getCrawlerLogs(crawlerId?: number, page: number = 1, pageSize: number = 10) {
    if (crawlerId) {
      // 获取指定采集器的日志
      const params = { page, pageSize }
      return api.get(`/admin/crawlers/${crawlerId}/logs`, { params })
    } else {
      // 获取所有采集器日志
      const params = { page, page_size: pageSize }
      return api.get('/admin/crawler-logs', { params })
    }
  },
  
  // 清除采集日志
  clearCrawlerLogs(crawlerId?: number) {
    const params: any = {}
    if (crawlerId) {
      params.crawlerId = crawlerId
    }
    return api.delete('/admin/crawler-logs', { params })
  },
  
  // 获取可用的采集器类型
  getCrawlerProviders() {
    return api.get('/admin/crawler-providers')
  },
  
  // 重置所有采集源
  resetCrawlers() {
    return api.post('/admin/crawler/reset')
  },

  // ========================================
  // IOC情报功能模块API - 按三个功能模块重新组织
  // 1. 数据采集模块 (Data Collection) - IOC源数据管理
  // 2. 情报生产模块 (Intelligence Production) - IOC情报管理
  // 3. 情报推送模块 (Intelligence Push) - IOC情报推送管理
  // ========================================

  // 1. 数据采集模块 (Data Collection) - IOC源数据管理

  // 获取IOC源数据列表
  getIOCSourceData(params?: {
    page?: number;
    page_size?: number;
    attack_ip?: string;
    victim_ip?: string;
    source_label?: string;
    category?: string;
  }) {
    return api.get('/ioc/data-collection/source-data', { params })
  },

  // 从数据接口生成IOC源数据
  generateIOCSourceData(data: {
    host: string;
    user_key: string;
    start_time: number;
    end_time: number;
  }) {
    return api.post('/ioc/data-collection/source-data/generate', data)
  },

  // 批量删除IOC源数据
  batchDeleteIOCSourceData(ids: number[]) {
    return api.delete('/ioc/data-collection/source-data/batch', { data: { ids } })
  },

  // 获取IOC源数据统计信息
  getIOCSourceDataStats() {
    return api.get('/ioc/data-collection/source-data/stats')
  },

  // 2. 情报生产模块 (Intelligence Production) - IOC情报管理

  // 获取IOC情报列表
  getIOCIntelligence(params?: {
    page?: number;
    page_size?: number;
    ioc?: string;
    ioc_type?: string;
    risk_level?: string;
    type?: string;
    target_org?: string;
    source?: string;
    hit_count_range?: string;
    start_date?: string;
    end_date?: string;
    keyword?: string;
    order_by?: string;
    order_dir?: string;
  }) {
    return api.get('/ioc/intelligence-production/intelligence', { params })
  },

  // 获取IOC情报详情
  getIOCIntelligenceDetail(id: number) {
    return api.get(`/ioc/intelligence-production/intelligence/${id}`)
  },

  // 创建IOC情报
  createIOCIntelligence(data: {
    ioc: string;
    iocType: string;
    location?: string;
    type: string;
    riskLevel: string;
    hitCount?: number;
    targetOrg?: string;
    description?: string;
    source: string;
    tags?: string;
    pushReason?: string;
    // 有效期相关字段
    validityDays?: number;
    isValid?: boolean;
  }) {
    return api.post('/ioc/intelligence-production/intelligence', data)
  },

  // 更新IOC情报
  updateIOCIntelligence(id: number, data: {
    ioc?: string;
    iocType?: string;
    location?: string;
    type?: string;
    riskLevel?: string;
    hitCount?: number;
    targetOrg?: string;
    description?: string;
    source?: string;
    tags?: string;
    pushReason?: string;
    // 有效期相关字段
    validityDays?: number;
    isValid?: boolean;
  }) {
    return api.put(`/ioc/intelligence-production/intelligence/${id}`, data)
  },

  // 删除IOC情报
  deleteIOCIntelligence(id: number) {
    return api.delete(`/ioc/intelligence-production/intelligence/${id}`)
  },

  // 批量删除IOC情报
  batchDeleteIOCIntelligence(ids: number[]) {
    return api.delete('/ioc/intelligence-production/intelligence/batch', { data: { ids } })
  },

  // 导出IOC情报
  exportIOCIntelligence(data: {
    ids?: number[];
    ioc?: string;
    ioc_type?: string;
    risk_level?: string;
    type?: string;
    source?: string;
    start_date?: string;
    end_date?: string;
    format?: string;
  }) {
    return api.post('/ioc/intelligence-production/intelligence/export', data, { responseType: 'blob' })
  },

  // 从源数据生成IOC情报
  generateIOCFromSource(data: {
    strategy_id?: number;
    time_range?: string;
  }) {
    return api.post('/ioc/intelligence-production/intelligence/generate-from-source', data)
  },

  // 获取生产策略配置
  getProductionStrategyConfig() {
    return api.get('/ioc/intelligence-production/production-strategy')
  },

  // 获取IOC白名单
  getIOCWhitelist(params?: {
    page?: number;
    page_size?: number;
    ioc?: string;
    ioc_type?: string;
    created_by?: string;
    keyword?: string;
    order_by?: string;
    order_dir?: string;
  }) {
    return api.get('/ioc/intelligence-production/whitelist', { params })
  },

  // 获取IOC情报统计信息
  getIOCIntelligenceStats() {
    return api.get('/ioc/intelligence-production/stats')
  },

  // 查询天际友盟IOC情报
  queryTJUNIOC(data: { ioc: string; iocType: string; iocId?: number }) {
    return api.post('/ioc/intelligence-production/tjun-query', data)
  },

  // 查询微步威胁情报IOC信息
  queryWeibuIOC(data: { ioc: string; iocId?: number }) {
    return api.post('/ioc/intelligence-production/weibu-query', data)
  },

  // 3. 情报推送模块 (Intelligence Push) - IOC情报推送管理

  // 单个IOC情报推送
  pushIOCIntelligence(id: number, channelId?: number, policyId?: number) {
    const data: any = {}
    if (channelId) data.channel_id = channelId
    if (policyId) data.policy_id = policyId

    return api.post(`/ioc/intelligence-push/single/${id}`, data)
  },

  // 批量IOC情报推送
  batchPushIOCIntelligence(data: {
    ioc_ids: number[];
    channel_id?: number;
    policy_id?: number;
  }) {
    return api.post('/ioc/intelligence-push/batch', data)
  },

  // 获取IOC情报推送记录
  getIOCIntelligencePushRecords(params?: {
    page?: number;
    page_size?: number;
    ioc?: string;
    status?: string;
    channel_type?: string;
    start_time?: string;
    end_time?: string;
  }) {
    return api.get('/ioc/intelligence-push/records', { params })
  },

  // 删除IOC情报推送记录
  deleteIOCIntelligencePushRecord(id: number) {
    return api.delete(`/ioc/intelligence-push/records/${id}`)
  },

  // 获取IOC情报推送统计信息
  getIOCIntelligencePushStats() {
    return api.get('/ioc/intelligence-push/records/stats')
  },

  // ========================================
  // 兼容性API - 保留旧的IOC白名单管理接口
  // ========================================

  // 获取IOC白名单详情
  getIOCWhitelistDetail(id: number) {
    return api.get(`/admin/ioc-whitelist/${id}`)
  },

  // 创建IOC白名单
  createIOCWhitelist(data: {
    ioc: string;
    iocType: string;
    remark?: string;
  }) {
    return api.post('/admin/ioc-whitelist', data)
  },

  // 更新IOC白名单
  updateIOCWhitelist(id: number, data: {
    ioc?: string;
    iocType?: string;
    remark?: string;
  }) {
    return api.put(`/admin/ioc-whitelist/${id}`, data)
  },

  // 删除IOC白名单
  deleteIOCWhitelist(id: number) {
    return api.delete(`/admin/ioc-whitelist/${id}`)
  },

  // 批量删除IOC白名单
  batchDeleteIOCWhitelist(ids: number[]) {
    return api.post('/admin/ioc-whitelist/batch-delete', { ids })
  },

  // 批量导入IOC白名单
  batchImportIOCWhitelist(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/admin/ioc-whitelist/batch-import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000, // 5分钟超时
      maxContentLength: 50 * 1024 * 1024, // 50MB最大文件大小
      maxBodyLength: 50 * 1024 * 1024,
      onUploadProgress: (progressEvent) => {
        // 可以在这里处理上传进度
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`上传进度: ${percentCompleted}%`)
        }
      }
    })
  },

  // 下载IOC白名单导入模板
  downloadIOCWhitelistTemplate() {
    return api.get('/admin/ioc-whitelist/template', {
      responseType: 'blob'
    })
  },

  // ========================================
  // 兼容性API - 保留旧的IOC情报管理接口
  // ========================================

  // 兼容性：导出IOC情报 (重定向到新的情报生产模块API)
  exportIOCIntelligenceCompat(data: {
    ids?: number[];
    ioc?: string;
    ioc_type?: string;
    risk_level?: string;
    type?: string;
    source?: string;
    start_date?: string;
    end_date?: string;
    format?: string;
  }) {
    return api.post('/admin/ioc-intelligence/export', data, { responseType: 'blob' })
  },

  // 数据接口管理相关接口

  // 获取数据接口列表
  getDataInterfaces(params?: {
    page?: number;
    page_size?: number;
    name?: string;
    type?: string;
    status?: string;
  }) {
    return api.get('/admin/data-interfaces', { params })
  },

  // 获取数据接口详情
  getDataInterfaceDetail(id: number) {
    return api.get(`/admin/data-interfaces/${id}`)
  },

  // 创建数据接口
  createDataInterface(data: {
    name: string;
    type: string;
    description?: string;
    config: any;
    collection_enabled?: boolean;
    collection_freq?: string;
    collection_cron?: string;
    time_range_type?: string;
    time_range_value?: number;
    start_time?: string;
    end_time?: string;
  }) {
    return api.post('/admin/data-interfaces', data)
  },

  // 更新数据接口
  updateDataInterface(id: number, data: {
    name?: string;
    type?: string;
    description?: string;
    config?: any;
    collection_enabled?: boolean;
    collection_freq?: string;
    collection_cron?: string;
    time_range_type?: string;
    time_range_value?: number;
    start_time?: string;
    end_time?: string;
  }) {
    return api.put(`/admin/data-interfaces/${id}`, data)
  },

  // 删除数据接口
  deleteDataInterface(id: number) {
    return api.delete(`/admin/data-interfaces/${id}`)
  },

  // 切换数据接口状态
  toggleDataInterfaceStatus(id: number) {
    return api.post(`/admin/data-interfaces/${id}/toggle`)
  },

  // 执行数据接口
  runDataInterface(id: number) {
    return api.post(`/admin/data-interfaces/${id}/run`)
  },

  // 获取数据接口执行日志
  getDataInterfaceLogs(id: number, params?: {
    page?: number;
    page_size?: number;
  }) {
    return api.get(`/admin/data-interfaces/${id}/logs`, { params })
  },

  // 获取数据接口统计信息
  getDataInterfaceStats() {
    return api.get('/admin/data-interfaces/stats')
  },

  // ========================================
  // 兼容性API - 保留旧的IOC情报数据接口（已迁移到数据采集模块）
  // ========================================

  // 兼容性：获取IOC情报数据列表 (重定向到新的源数据API)
  getIOCIntelligenceData(params?: {
    page?: number;
    page_size?: number;
    attack_ip?: string;
    victim_ip?: string;
    source_label?: string;
    category?: string;
  }) {
    // 重定向到新的数据采集模块API
    return api.get('/ioc/data-collection/source-data', { params })
  },

  // 兼容性：生成IOC情报数据 (重定向到新的源数据生成API)
  generateIOCIntelligenceData(data: {
    host: string;
    user_key: string;
    start_time: number;
    end_time: number;
  }) {
    // 重定向到新的数据采集模块API
    return api.post('/ioc/data-collection/source-data/generate', data)
  },

  // 兼容性：批量删除IOC情报数据 (重定向到新的源数据删除API)
  batchDeleteIOCIntelligenceData(ids: number[]) {
    // 重定向到新的数据采集模块API
    return api.delete('/ioc/data-collection/source-data/batch', { data: { ids } })
  },

  // 兼容性：清除UUID去重记录
  clearUUIDRecords() {
    return api.delete('/admin/ioc-intelligence-data/clear-uuid-records')
  },

  // ==================== 生产策略相关API ====================

  // 获取生产策略列表
  getProductionStrategies(params?: any) {
    return api.get('/admin/production-strategies', { params })
  },

  // 兼容性：获取生产策略全局配置 (重定向到新的情报生产模块API)
  getProductionStrategyGlobalConfig() {
    return api.get('/admin/production-strategy-config')
  },

  // 创建生产策略
  createProductionStrategy(data: any) {
    return api.post('/admin/production-strategies', data)
  },

  // 更新生产策略
  updateProductionStrategy(id: number, data: any) {
    return api.put(`/admin/production-strategies/${id}`, data)
  },

  // 删除生产策略
  deleteProductionStrategy(id: number) {
    return api.delete(`/admin/production-strategies/${id}`)
  },

  // 切换生产策略状态
  toggleProductionStrategyStatus(id: number) {
    return api.post(`/admin/production-strategies/${id}/toggle-status`)
  },

  // 执行生产策略
  executeProductionStrategy(id: number) {
    return api.post(`/admin/production-strategies/${id}/execute`)
  },

  // 获取生产策略执行日志
  getProductionStrategyLogs(strategyId: number, params?: any) {
    return api.get(`/admin/production-strategies/${strategyId}/logs`, { params })
  }
}