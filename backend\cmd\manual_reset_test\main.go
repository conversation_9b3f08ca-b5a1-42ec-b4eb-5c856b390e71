package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 手动重置满足条件的已处理数据
	fmt.Println("\n🔄 手动重置满足条件的已处理数据:")
	
	result := db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "processed").
		Updates(map[string]interface{}{
			"processed_status": "unprocessed",
			"processed_at":     0,
		})
	
	if result.Error != nil {
		log.Fatalf("重置失败: %v", result.Error)
	}
	
	fmt.Printf("✅ 成功重置 %d 条记录\n", result.RowsAffected)
	
	// 检查重置后的状态
	fmt.Println("\n📊 重置后的状态检查:")
	
	var readyCount int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&readyCount)
	fmt.Printf("满足生产条件且未处理: %d 条\n", readyCount)
	
	// 显示满足条件的数据
	fmt.Println("\n📋 满足生产条件且未处理的数据:")
	
	type SourceData struct {
		ID              uint    `json:"id"`
		AttackIP        string  `json:"attack_ip"`
		VictimIP        string  `json:"victim_ip"`
		AttackCount     int     `json:"attack_count"`
		ThreatScore     float64 `json:"threat_score"`
		ProcessedStatus string  `json:"processed_status"`
		Category        string  `json:"category"`
	}
	
	var samples []SourceData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Find(&samples)
	
	for _, sample := range samples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
	
	fmt.Println("\n🎉 现在可以重新执行生产策略了！")
}
