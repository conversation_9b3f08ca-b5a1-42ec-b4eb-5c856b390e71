package handlers

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"vulnerability_push/internal/models"
	"vulnerability_push/IOC_Feed"
)

// GetIOCIntelligenceRequest 获取IOC情报请求
type GetIOCIntelligenceRequest struct {
	Page        int    `form:"page"`
	PageSize    int    `form:"page_size"`
	IOC         string `form:"ioc"`
	IOCType     string `form:"ioc_type"`
	RiskLevel   string `form:"risk_level"`
	Type        string `form:"type"`
	TargetOrg   string `form:"target_org"`
	Source      string `form:"source"`
	HitCountRange string `form:"hit_count_range"`
	StartDate   string `form:"start_date"`
	EndDate     string `form:"end_date"`
	Keyword     string `form:"keyword"`
	OrderBy     string `form:"order_by"`
	OrderDir    string `form:"order_dir"`
}

// NormalizePagination 标准化分页参数
func (req *GetIOCIntelligenceRequest) NormalizePagination() {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
}

// CreateIOCIntelligenceRequest 创建IOC情报请求
type CreateIOCIntelligenceRequest struct {
	IOC           string `json:"ioc" binding:"required"`
	IOCType       string `json:"iocType" binding:"required"`
	Location      string `json:"location"`
	Type          string `json:"type" binding:"required"`
	RiskLevel     string `json:"riskLevel" binding:"required"`
	HitCount      int    `json:"hitCount"`
	Description   string `json:"description"`
	Source        string `json:"source" binding:"required"`
	Tags          string `json:"tags"`
	PushReason    string `json:"pushReason"`

	// 有效期相关字段
	ValidityDays  int  `json:"validityDays"`  // 有效期天数，0表示永不过期
	IsValid       bool `json:"isValid"`       // 是否有效，默认true
}

// UpdateIOCIntelligenceRequest 更新IOC情报请求
type UpdateIOCIntelligenceRequest struct {
	IOC           *string `json:"ioc"`
	IOCType       *string `json:"iocType"`
	Location      *string `json:"location"`
	Type          *string `json:"type"`
	RiskLevel     *string `json:"riskLevel"`
	HitCount      *int    `json:"hitCount"`
	Description   *string `json:"description"`
	Source        *string `json:"source"`
	Tags          *string `json:"tags"`
	PushReason    *string `json:"pushReason"`

	// 有效期相关字段
	ValidityDays  *int  `json:"validityDays"`  // 有效期天数，0表示永不过期
	IsValid       *bool `json:"isValid"`       // 是否有效
}

// GetIOCIntelligence 获取IOC情报列表
func (h *IOCHandler) GetIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req GetIOCIntelligenceRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 标准化分页参数
	req.NormalizePagination()
	if req.PageSize <= 0 {
		req.PageSize = 20 // IOC情报默认每页20条
	}
	if req.OrderBy == "" {
		req.OrderBy = "created_at"
	}
	if req.OrderDir == "" {
		req.OrderDir = "desc"
	}

	// 构建查询
	query := h.db.Model(&models.IOCIntelligence{})

	// 应用过滤条件
	if req.IOC != "" {
		query = query.Where("ioc LIKE ?", "%"+req.IOC+"%")
	}
	if req.IOCType != "" {
		query = query.Where("ioc_type = ?", req.IOCType)
	}
	if req.RiskLevel != "" {
		query = query.Where("risk_level = ?", req.RiskLevel)
	}
	if req.Type != "" {
		query = query.Where("type LIKE ?", "%"+req.Type+"%")
	}
	if req.TargetOrg != "" {
		query = query.Where("target_org LIKE ?", "%"+req.TargetOrg+"%")
	}
	if req.Source != "" {
		query = query.Where("source LIKE ?", "%"+req.Source+"%")
	}
	if req.Keyword != "" {
		query = query.Where("(ioc LIKE ? OR description LIKE ? OR tags LIKE ?)", 
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 日期范围过滤
	if req.StartDate != "" {
		if startTime, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			query = query.Where("created_at >= ?", startTime.Unix())
		}
	}
	if req.EndDate != "" {
		if endTime, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			query = query.Where("created_at <= ?", endTime.AddDate(0, 0, 1).Unix())
		}
	}

	// 命中次数范围过滤
	if req.HitCountRange != "" {
		parts := strings.Split(req.HitCountRange, "-")
		if len(parts) == 2 {
			if min, err := strconv.Atoi(parts[0]); err == nil {
				query = query.Where("hit_count >= ?", min)
			}
			if max, err := strconv.Atoi(parts[1]); err == nil {
				query = query.Where("hit_count <= ?", max)
			}
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报总数失败: "+err.Error())
		return
	}

	// 应用排序和分页
	var iocIntelligence []models.IOCIntelligence
	offset := (req.Page - 1) * req.PageSize
	orderClause := req.OrderBy + " " + req.OrderDir
	
	if err := query.Order(orderClause).Offset(offset).Limit(req.PageSize).Find(&iocIntelligence).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报列表失败: "+err.Error())
		return
	}

	// 为每个IOC情报获取最新的推送记录
	responseData := make([]map[string]interface{}, len(iocIntelligence))
	for i, ioc := range iocIntelligence {
		// 获取最新的推送记录
		var lastPushRecord models.IOCIntelligencePushRecord
		err := h.db.Where("ioc_intelligence_id = ?", ioc.ID).
			Order("pushed_at DESC").
			First(&lastPushRecord).Error

		// 构建响应数据
		iocData := map[string]interface{}{
			"id":            ioc.ID,
			"ioc":           ioc.IOC,
			"iocType":       ioc.IOCType,
			"location":      ioc.Location,
			"type":          ioc.Type,
			"riskLevel":     ioc.RiskLevel,
			"hitCount":      ioc.HitCount,
			"description":   ioc.Description,
			"tags":          ioc.Tags,
			"pushStatus":    ioc.PushStatus,
			"pushedAt":      ioc.PushedAt,
			"source":        ioc.Source,
			"pushReason":    ioc.PushReason,
			"createdAt":     ioc.CreatedAt,
			"updatedAt":     ioc.UpdatedAt,
		}

		// 如果找到推送记录，添加到响应数据中
		if err == nil {
			iocData["lastPushRecord"] = map[string]interface{}{
				"id":           lastPushRecord.ID,
				"channelId":    lastPushRecord.ChannelID,
				"channelName":  lastPushRecord.ChannelName,
				"channelType":  lastPushRecord.ChannelType,
				"status":       lastPushRecord.Status,
				"errorMessage": lastPushRecord.ErrorMessage,
				"pushedAt":     lastPushRecord.PushedAt,
			}
		}

		responseData[i] = iocData
	}

	h.PaginatedSuccess(c, responseData, total, req.Page, req.PageSize)
}

// GetIOCIntelligenceByID 获取单个IOC情报
func (h *IOCHandler) GetIOCIntelligenceByID(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var iocIntelligence models.IOCIntelligence
	if err := h.db.First(&iocIntelligence, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC情报不存在")
			return
		}
		h.InternalServerError(c, "获取IOC情报失败: "+err.Error())
		return
	}

	h.Success(c, iocIntelligence)
}

// CreateIOCIntelligence 创建IOC情报
func (h *IOCHandler) CreateIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req CreateIOCIntelligenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查IOC是否已存在
	var existingCount int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("ioc = ?", req.IOC).Count(&existingCount).Error; err != nil {
		h.InternalServerError(c, "检查IOC是否存在失败: "+err.Error())
		return
	}
	if existingCount > 0 {
		h.BadRequest(c, "IOC已存在")
		return
	}

	// 如果是IP类型且没有提供地理位置，自动查询地理位置
	location := req.Location
	if strings.ToLower(req.IOCType) == "ip" && location == "" {
		location = IOC_Feed.GetIPLocation(req.IOC)
	}

	// 自动查询天际友盟情报（单个IOC查询）
	var tjunData string
	var tjunQueryStatus string
	var tjunQueryTime int64
	var tjunErrorMessage string

	tjunResult := IOC_Feed.QueryIOCIntelligenceGlobal(req.IOC, req.IOCType)
	if tjunResult != nil {
		tjunQueryTime = tjunResult.QueryTime
		tjunQueryStatus = tjunResult.GetQueryStatus()

		if tjunResult.Success {
			if serializedData, err := tjunResult.SerializeData(); err == nil {
				tjunData = serializedData
			} else {
				tjunErrorMessage = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
			}
		} else {
			tjunErrorMessage = tjunResult.ErrorMessage
		}
	} else {
		tjunQueryStatus = "failed"
		tjunErrorMessage = "天际友盟服务不可用"
	}

	// 自动查询微步威胁情报（仅对IP类型）
	var weibuData string
	var weibuQueryStatus string
	var weibuQueryTime int64
	var weibuErrorMessage string

	if strings.ToLower(req.IOCType) == "ip" {
		weibuResult := IOC_Feed.QueryWeibuIPReputationGlobal(req.IOC)
		if weibuResult != nil {
			weibuQueryTime = weibuResult.QueryTime
			weibuQueryStatus = weibuResult.GetQueryStatus()

			if weibuResult.Success {
				if serializedData, err := weibuResult.SerializeData(); err == nil {
					weibuData = serializedData
				} else {
					weibuErrorMessage = fmt.Sprintf("序列化微步威胁情报数据失败: %v", err)
				}
			} else {
				weibuErrorMessage = weibuResult.ErrorMessage
			}
		} else {
			weibuQueryStatus = "failed"
			weibuErrorMessage = "微步威胁情报服务不可用"
		}
	} else {
		weibuQueryStatus = "not_queried"
		weibuErrorMessage = "微步威胁情报仅支持IP类型查询"
	}

	// 创建IOC情报
	iocIntelligence := models.IOCIntelligence{
		IOC:               req.IOC,
		IOCType:           req.IOCType,
		Location:          location,
		Type:              req.Type,
		RiskLevel:         req.RiskLevel,
		HitCount:          req.HitCount,
		Description:       req.Description,
		Source:            req.Source,
		Tags:              req.Tags,
		PushReason:        req.PushReason,
		PushStatus:        "not_pushed", // 默认未推送状态

		// 有效期相关字段
		IsValid:           req.IsValid,
		ValidityDays:      req.ValidityDays,

		TJUNData:          tjunData,
		TJUNQueryStatus:   tjunQueryStatus,
		TJUNQueryTime:     tjunQueryTime,
		TJUNErrorMessage:  tjunErrorMessage,
		WeibuData:         weibuData,
		WeibuQueryStatus:  weibuQueryStatus,
		WeibuQueryTime:    weibuQueryTime,
		WeibuErrorMessage: weibuErrorMessage,
	}

	// 设置有效期
	if req.ValidityDays > 0 {
		iocIntelligence.SetValidityPeriod(req.ValidityDays)
	} else {
		// 如果没有指定有效期，使用默认值（30天）
		iocIntelligence.SetValidityPeriod(30)
	}

	if err := h.db.Create(&iocIntelligence).Error; err != nil {
		h.InternalServerError(c, "创建IOC情报失败: "+err.Error())
		return
	}

	h.Success(c, iocIntelligence)
}

// UpdateIOCIntelligence 更新IOC情报
func (h *IOCHandler) UpdateIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	var req UpdateIOCIntelligenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 获取现有IOC情报
	var iocIntelligence models.IOCIntelligence
	if err := h.db.First(&iocIntelligence, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC情报不存在")
			return
		}
		h.InternalServerError(c, "获取IOC情报失败: "+err.Error())
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.IOC != nil {
		updates["ioc"] = *req.IOC
	}
	if req.IOCType != nil {
		updates["ioc_type"] = *req.IOCType
	}
	if req.Location != nil {
		updates["location"] = *req.Location
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.RiskLevel != nil {
		updates["risk_level"] = *req.RiskLevel
	}
	if req.HitCount != nil {
		updates["hit_count"] = *req.HitCount
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Source != nil {
		updates["source"] = *req.Source
	}
	if req.Tags != nil {
		updates["tags"] = *req.Tags
	}
	if req.PushReason != nil {
		updates["push_reason"] = *req.PushReason
	}

	// 有效期相关字段
	if req.IsValid != nil {
		updates["is_valid"] = *req.IsValid
	}
	if req.ValidityDays != nil {
		updates["validity_days"] = *req.ValidityDays
		// 如果更新了有效期天数，重新计算过期时间
		if *req.ValidityDays > 0 {
			updates["expires_at"] = time.Now().AddDate(0, 0, *req.ValidityDays).Unix()
		} else {
			updates["expires_at"] = 0 // 永不过期
		}
	}

	if err := h.db.Model(&iocIntelligence).Updates(updates).Error; err != nil {
		h.InternalServerError(c, "更新IOC情报失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "IOC情报更新成功"})
}

// DeleteIOCIntelligence 删除IOC情报
func (h *IOCHandler) DeleteIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	id, err := h.GetIDParam(c, "id")
	if err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查IOC情报是否存在
	var iocIntelligence models.IOCIntelligence
	if err := h.db.First(&iocIntelligence, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.NotFound(c, "IOC情报不存在")
			return
		}
		h.InternalServerError(c, "获取IOC情报失败: "+err.Error())
		return
	}

	// 使用事务确保数据一致性
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 先删除相关的推送记录
	if err := tx.Where("ioc_intelligence_id = ?", id).Delete(&models.IOCIntelligencePushRecord{}).Error; err != nil {
		tx.Rollback()
		h.InternalServerError(c, "删除相关推送记录失败: "+err.Error())
		return
	}

	// 2. 重置对应源数据的处理状态（如果存在processed_status字段）
	if h.hasProcessedStatusColumn() {
		// 根据IOC值找到对应的源数据并重置状态
		resetResult := tx.Model(&models.IOCIntelligenceData{}).
			Where("attack_ip = ? AND processed_status = ?", iocIntelligence.IOC, "processed").
			Updates(map[string]interface{}{
				"processed_status": "unprocessed",
				"processed_at":     0,
			})

		if resetResult.Error != nil {
			tx.Rollback()
			h.InternalServerError(c, "重置源数据状态失败: "+resetResult.Error.Error())
			return
		}

		if resetResult.RowsAffected > 0 {
			fmt.Printf("🔄 重置了 %d 条源数据的处理状态为未处理\n", resetResult.RowsAffected)
		}
	}

	// 3. 再删除IOC情报
	if err := tx.Delete(&iocIntelligence).Error; err != nil {
		tx.Rollback()
		h.InternalServerError(c, "删除IOC情报失败: "+err.Error())
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		h.InternalServerError(c, "删除操作提交失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{"message": "IOC情报删除成功"})
}

// BatchDeleteIOCIntelligenceRequest 批量删除IOC情报请求
type BatchDeleteIOCIntelligenceRequest struct {
	IDs []uint `json:"ids" binding:"required,min=1"`
}

// BatchDeleteIOCIntelligence 批量删除IOC情报
func (h *IOCHandler) BatchDeleteIOCIntelligence(c *gin.Context) {
	if !h.RequireAdmin(c) {
		return
	}

	var req BatchDeleteIOCIntelligenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 检查所有IOC情报是否存在
	var count int64
	if err := h.db.Model(&models.IOCIntelligence{}).Where("id IN ?", req.IDs).Count(&count).Error; err != nil {
		h.InternalServerError(c, "查询IOC情报失败: "+err.Error())
		return
	}

	if int(count) != len(req.IDs) {
		h.BadRequest(c, "部分IOC情报不存在")
		return
	}

	// 使用事务确保数据一致性
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 先删除相关的推送记录
	if err := tx.Where("ioc_intelligence_id IN ?", req.IDs).Delete(&models.IOCIntelligencePushRecord{}).Error; err != nil {
		tx.Rollback()
		h.InternalServerError(c, "删除相关推送记录失败: "+err.Error())
		return
	}

	// 2. 获取要删除的IOC情报的IOC值，用于重置源数据状态
	var iocValues []string
	if h.hasProcessedStatusColumn() {
		if err := tx.Model(&models.IOCIntelligence{}).
			Where("id IN ?", req.IDs).
			Pluck("ioc", &iocValues).Error; err != nil {
			tx.Rollback()
			h.InternalServerError(c, "获取IOC值失败: "+err.Error())
			return
		}
	}

	// 3. 重置对应源数据的处理状态
	if h.hasProcessedStatusColumn() && len(iocValues) > 0 {
		resetResult := tx.Model(&models.IOCIntelligenceData{}).
			Where("attack_ip IN ? AND processed_status = ?", iocValues, "processed").
			Updates(map[string]interface{}{
				"processed_status": "unprocessed",
				"processed_at":     0,
			})

		if resetResult.Error != nil {
			tx.Rollback()
			h.InternalServerError(c, "重置源数据状态失败: "+resetResult.Error.Error())
			return
		}

		if resetResult.RowsAffected > 0 {
			fmt.Printf("🔄 批量重置了 %d 条源数据的处理状态为未处理\n", resetResult.RowsAffected)
		}
	}

	// 4. 再批量删除IOC情报
	result := tx.Where("id IN ?", req.IDs).Delete(&models.IOCIntelligence{})
	if result.Error != nil {
		tx.Rollback()
		h.InternalServerError(c, "批量删除IOC情报失败: "+result.Error.Error())
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		h.InternalServerError(c, "批量删除操作提交失败: "+err.Error())
		return
	}

	h.Success(c, gin.H{
		"message":       "批量删除成功",
		"deleted_count": result.RowsAffected,
	})
}

// hasProcessedStatusColumn 检查是否存在processed_status字段
func (h *IOCHandler) hasProcessedStatusColumn() bool {
	// 使用GORM的Migrator检查字段是否存在
	return h.db.Migrator().HasColumn(&models.IOCIntelligenceData{}, "processed_status")
}

// ExportIOCIntelligenceRequest 导出IOC情报请求
type ExportIOCIntelligenceRequest struct {
	IDs       []uint `json:"ids"`        // 指定导出的IOC情报ID列表，为空则导出所有
	IOC       string `json:"ioc"`        // IOC值过滤
	IOCType   string `json:"ioc_type"`   // IOC类型过滤
	RiskLevel string `json:"risk_level"` // 风险等级过滤
	Type      string `json:"type"`       // 情报类型过滤
	Source    string `json:"source"`     // 来源过滤
	StartDate string `json:"start_date"` // 开始日期
	EndDate   string `json:"end_date"`   // 结束日期
	Format    string `json:"format"`     // 导出格式：csv, excel
}

// ExportIOCIntelligence 导出IOC情报
func (h *IOCHandler) ExportIOCIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req ExportIOCIntelligenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 默认导出格式为CSV
	if req.Format == "" {
		req.Format = "csv"
	}

	// 构建查询
	query := h.db.Model(&models.IOCIntelligence{})

	// 如果指定了ID列表，只导出指定的记录
	if len(req.IDs) > 0 {
		query = query.Where("id IN ?", req.IDs)
	} else {
		// 否则应用过滤条件
		if req.IOC != "" {
			query = query.Where("ioc LIKE ?", "%"+req.IOC+"%")
		}
		if req.IOCType != "" {
			query = query.Where("ioc_type = ?", req.IOCType)
		}
		if req.RiskLevel != "" {
			query = query.Where("risk_level = ?", req.RiskLevel)
		}
		if req.Type != "" {
			query = query.Where("type LIKE ?", "%"+req.Type+"%")
		}
		if req.Source != "" {
			query = query.Where("source LIKE ?", "%"+req.Source+"%")
		}

		// 时间范围过滤
		if req.StartDate != "" {
			if startTime, err := time.Parse("2006-01-02", req.StartDate); err == nil {
				query = query.Where("created_at >= ?", startTime.Unix())
			}
		}
		if req.EndDate != "" {
			if endTime, err := time.Parse("2006-01-02", req.EndDate); err == nil {
				// 结束日期包含当天，所以加一天
				endTime = endTime.AddDate(0, 0, 1)
				query = query.Where("created_at < ?", endTime.Unix())
			}
		}
	}

	// 获取数据
	var iocIntelligence []models.IOCIntelligence
	if err := query.Order("created_at DESC").Find(&iocIntelligence).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报数据失败: "+err.Error())
		return
	}

	// 检查是否有数据
	if len(iocIntelligence) == 0 {
		h.BadRequest(c, "没有找到符合条件的IOC情报数据")
		return
	}

	// 根据格式导出
	switch req.Format {
	case "csv":
		h.exportIOCIntelligenceAsCSV(c, iocIntelligence)
	case "excel":
		h.BadRequest(c, "Excel格式导出暂未实现")
	default:
		h.BadRequest(c, "不支持的导出格式")
	}
}

// exportIOCIntelligenceAsCSV 导出IOC情报为CSV格式
func (h *IOCHandler) exportIOCIntelligenceAsCSV(c *gin.Context, data []models.IOCIntelligence) {
	// 生成文件名
	now := time.Now()
	filename := fmt.Sprintf("ioc_intelligence_%s.csv", now.Format("20060102_150405"))

	// 设置响应头
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Cache-Control", "no-cache")

	// 添加UTF-8 BOM，确保Excel能正确识别中文
	c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})

	// 创建CSV写入器
	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入表头
	headers := []string{
		"ID", "IOC值", "IOC类型", "地理位置", "情报类型", "风险等级",
		"命中次数", "推送状态", "推送时间", "描述", "来源", "标签", "推送原因",
		"创建时间", "更新时间",
	}
	if err := writer.Write(headers); err != nil {
		h.InternalServerError(c, "写入CSV表头失败: "+err.Error())
		return
	}

	// 写入数据行
	for _, item := range data {
		// 格式化推送时间
		var pushTimeStr string
		if item.PushedAt > 0 {
			pushTimeStr = time.Unix(item.PushedAt, 0).Format("2006-01-02 15:04:05")
		} else {
			pushTimeStr = ""
		}

		record := []string{
			fmt.Sprintf("%d", item.ID),
			item.IOC,
			item.IOCType,
			item.Location,
			item.Type,
			item.RiskLevel,
			fmt.Sprintf("%d", item.HitCount),
			item.GetPushStatusText(),
			pushTimeStr,
			item.Description,
			item.Source,
			item.Tags,
			item.PushReason,
			time.Unix(item.CreatedAt, 0).Format("2006-01-02 15:04:05"),
			time.Unix(item.UpdatedAt, 0).Format("2006-01-02 15:04:05"),
		}
		if err := writer.Write(record); err != nil {
			h.InternalServerError(c, "写入CSV数据失败: "+err.Error())
			return
		}
	}
}

// TJUNIOCQueryRequest 天际友盟IOC查询请求
type TJUNIOCQueryRequest struct {
	IOC     string `json:"ioc" binding:"required"`     // IOC值
	IOCType string `json:"iocType" binding:"required"` // IOC类型：ip, domain等
	IOCId   uint   `json:"iocId,omitempty"`            // IOC情报ID（可选，用于更新数据库记录）
}

// TJUNIOCQueryResponse 天际友盟IOC查询响应
type TJUNIOCQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// QueryTJUNIOC 查询天际友盟IOC情报
func (h *IOCHandler) QueryTJUNIOC(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req TJUNIOCQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 创建天际友盟客户端
	// TODO: 这些配置应该从配置文件或环境变量中读取
	const (
		HOST       = "api.tj-un.com"
		APP_KEY    = "24634572"
		APP_SECRET = "812d078fe8143e529599f9d6689a6046"
		TOKEN      = "9fd3b7f517dc4674b7f001e0b1ed7c61"
	)

	client := IOC_Feed.NewTJUNClient(HOST, APP_KEY, APP_SECRET)
	client.SetTimeout(10 * time.Second)

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// 根据IOC类型调用不同的接口
	var response interface{}

	if strings.ToLower(req.IOCType) == "ip" {
		// 对于IP类型，调用reputation接口
		ipArray := fmt.Sprintf(`["%s"]`, req.IOC)
		reputationResp, err := client.RequestIOCReputation(ctx, TOKEN, "ip", ipArray, "ip_reputation", "")
		if err != nil {
			h.BadRequest(c, "查询天际友盟IOC失败: "+err.Error())
			return
		}
		response = reputationResp.ResponseData
	} else {
		// 对于其他类型，调用requestIOC接口
		iocResp, err := client.RequestIOCV2(ctx, TOKEN, req.IOCType, req.IOC)
		if err != nil {
			h.BadRequest(c, "查询天际友盟IOC失败: "+err.Error())
			return
		}
		response = iocResp.ResponseData
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		updateData := map[string]interface{}{
			"tjun_query_time":   time.Now().Unix(),
			"tjun_query_status": "success",
			"tjun_error_message": "",
		}

		if serializedData, err := json.Marshal(response); err == nil {
			updateData["tjun_data"] = string(serializedData)
		} else {
			updateData["tjun_error_message"] = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
		}

		// 更新数据库记录
		if err := h.db.Model(&models.IOCIntelligence{}).Where("id = ?", req.IOCId).Updates(updateData).Error; err != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报天际友盟数据失败: %v\n", err)
		}
	}

	// 返回结果
	result := TJUNIOCQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    response,
	}

	c.JSON(http.StatusOK, result)
}

// WeibuIOCQueryRequest 微步威胁情报IOC查询请求
type WeibuIOCQueryRequest struct {
	IOC   string `json:"ioc" binding:"required"`   // IOC值
	IOCId uint   `json:"iocId,omitempty"`          // IOC情报ID（可选，用于更新数据库记录）
}

// WeibuIOCQueryResponse 微步威胁情报IOC查询响应
type WeibuIOCQueryResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// QueryWeibuIOC 查询微步威胁情报IOC信息
func (h *IOCHandler) QueryWeibuIOC(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	var req WeibuIOCQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.ValidationError(c, err)
		return
	}

	// 调用微步威胁情报查询服务
	weibuResult := IOC_Feed.QueryWeibuIPReputationGlobal(req.IOC)
	if weibuResult == nil {
		h.InternalServerError(c, "微步威胁情报服务不可用")
		return
	}

	if !weibuResult.Success {
		h.BadRequest(c, "查询微步威胁情报失败: "+weibuResult.ErrorMessage)
		return
	}

	// 如果提供了IOC ID，更新数据库记录
	if req.IOCId > 0 {
		updateData := map[string]interface{}{
			"weibu_query_time":   time.Now().Unix(),
			"weibu_query_status": "success",
			"weibu_error_message": "",
		}

		if serializedData, err := weibuResult.SerializeData(); err == nil {
			updateData["weibu_data"] = serializedData
		} else {
			updateData["weibu_error_message"] = fmt.Sprintf("序列化微步数据失败: %v", err)
		}

		// 更新数据库记录
		if err := h.db.Model(&models.IOCIntelligence{}).Where("id = ?", req.IOCId).Updates(updateData).Error; err != nil {
			// 记录错误但不影响返回结果
			fmt.Printf("更新IOC情报微步数据失败: %v\n", err)
		}
	}

	// 返回结果
	result := WeibuIOCQueryResponse{
		Success: true,
		Message: "查询成功",
		Data:    weibuResult.Data,
	}

	c.JSON(http.StatusOK, result)
}

// BatchUpdateTJUNIntelligence 批量更新IOC情报的天际友盟数据
func (h *IOCHandler) BatchUpdateTJUNIntelligence(c *gin.Context) {
	if !h.RequireAuth(c) {
		return
	}

	// 获取所有未查询天际友盟情报的IOC记录
	var iocList []models.IOCIntelligence
	if err := h.db.Where("tjun_query_status = ? OR tjun_query_status IS NULL", "not_queried").Find(&iocList).Error; err != nil {
		h.InternalServerError(c, "获取IOC情报列表失败: "+err.Error())
		return
	}

	if len(iocList) == 0 {
		h.Success(c, gin.H{
			"message": "没有需要更新的IOC情报",
			"updated_count": 0,
		})
		return
	}

	updatedCount := 0
	failedCount := 0

	for _, ioc := range iocList {
		// 查询天际友盟情报
		tjunResult := IOC_Feed.QueryIOCIntelligenceGlobal(ioc.IOC, ioc.IOCType)

		updateData := map[string]interface{}{
			"tjun_query_time": time.Now().Unix(),
		}

		if tjunResult != nil {
			updateData["tjun_query_status"] = tjunResult.GetQueryStatus()

			if tjunResult.Success {
				if serializedData, err := tjunResult.SerializeData(); err == nil {
					updateData["tjun_data"] = serializedData
					updateData["tjun_error_message"] = ""
				} else {
					updateData["tjun_error_message"] = fmt.Sprintf("序列化天际友盟数据失败: %v", err)
				}
			} else {
				updateData["tjun_error_message"] = tjunResult.ErrorMessage
			}
		} else {
			updateData["tjun_query_status"] = "failed"
			updateData["tjun_error_message"] = "天际友盟服务不可用"
		}

		// 更新数据库记录
		if err := h.db.Model(&ioc).Updates(updateData).Error; err != nil {
			failedCount++
			continue
		}

		updatedCount++

		// 添加短暂延迟，避免API调用过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	h.Success(c, gin.H{
		"message": fmt.Sprintf("批量更新完成，成功: %d, 失败: %d", updatedCount, failedCount),
		"updated_count": updatedCount,
		"failed_count": failedCount,
		"total_count": len(iocList),
	})
}
