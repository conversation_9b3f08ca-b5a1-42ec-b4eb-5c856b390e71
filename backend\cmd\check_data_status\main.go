package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 检查IOC情报表
	fmt.Println("\n📊 检查IOC情报表:")
	var iocCount int64
	db.Table("ioc_intelligence").Count(&iocCount)
	fmt.Printf("IOC情报总数: %d\n", iocCount)
	
	// 检查IOC源数据表
	fmt.Println("\n📊 检查IOC源数据表:")
	var sourceCount int64
	db.Table("ioc_source_data").Count(&sourceCount)
	fmt.Printf("IOC源数据总数: %d\n", sourceCount)
	
	// 检查源数据的处理状态分布
	fmt.Println("\n📊 源数据处理状态分布:")
	
	type StatusCount struct {
		ProcessedStatus string `json:"processed_status"`
		Count          int64  `json:"count"`
	}
	
	var statusCounts []StatusCount
	err = db.Table("ioc_source_data").
		Select("processed_status, COUNT(*) as count").
		Group("processed_status").
		Find(&statusCounts).Error
	
	if err != nil {
		log.Fatalf("查询处理状态分布失败: %v", err)
	}
	
	for _, sc := range statusCounts {
		fmt.Printf("  %s: %d 条\n", sc.ProcessedStatus, sc.Count)
	}
	
	// 检查满足生产条件的数据
	fmt.Println("\n🔍 检查满足生产条件的数据:")
	
	var readyCount int64
	db.Table("ioc_source_data").
		Where("attack_count >= ? AND threat_score >= ? AND processed_status = ?", 3, 2, "unprocessed").
		Count(&readyCount)
	fmt.Printf("满足生产条件且未处理: %d 条\n", readyCount)
	
	// 显示一些具体的源数据示例
	fmt.Println("\n📋 源数据示例:")
	
	type SourceData struct {
		ID              uint    `json:"id"`
		AttackIP        string  `json:"attack_ip"`
		VictimIP        string  `json:"victim_ip"`
		AttackCount     int     `json:"attack_count"`
		ThreatScore     float64 `json:"threat_score"`
		ProcessedStatus string  `json:"processed_status"`
		Category        string  `json:"category"`
	}
	
	var samples []SourceData
	db.Table("ioc_source_data").
		Select("id, attack_ip, victim_ip, attack_count, threat_score, processed_status, category").
		Limit(5).
		Find(&samples)
	
	for _, sample := range samples {
		fmt.Printf("  ID:%d, IP:%s->%s, 攻击次数:%d, 威胁评分:%.2f, 状态:%s, 类别:%s\n",
			sample.ID, sample.AttackIP, sample.VictimIP, sample.AttackCount, 
			sample.ThreatScore, sample.ProcessedStatus, sample.Category)
	}
}
