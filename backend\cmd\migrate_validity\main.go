package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strings"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 数据库连接配置
	dsn := "root:Tisec_Hjzd@2025@tcp(127.0.0.1:3306)/SOC_CenterDB?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 读取迁移脚本
	scriptPath := filepath.Join("migrations", "add_ioc_validity_fields.sql")
	content, err := ioutil.ReadFile(scriptPath)
	if err != nil {
		log.Fatalf("读取迁移脚本失败: %v", err)
	}

	fmt.Printf("📄 读取迁移脚本: %s\n", scriptPath)

	// 分割SQL语句（按分号分割，但要处理存储过程中的分号）
	sqlContent := string(content)
	statements := splitSQLStatements(sqlContent)

	fmt.Printf("📊 共找到 %d 个SQL语句\n", len(statements))

	// 执行每个SQL语句
	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		fmt.Printf("\n🔄 执行语句 %d: %s\n", i+1, truncateSQL(stmt, 100))
		
		if err := db.Exec(stmt).Error; err != nil {
			// 检查是否是字段已存在的错误
			if strings.Contains(err.Error(), "Duplicate column name") {
				fmt.Printf("⚠️  字段已存在，跳过: %v\n", err)
				continue
			}
			// 检查是否是索引已存在的错误
			if strings.Contains(err.Error(), "Duplicate key name") {
				fmt.Printf("⚠️  索引已存在，跳过: %v\n", err)
				continue
			}
			// 检查是否是存储过程已存在的错误
			if strings.Contains(err.Error(), "already exists") {
				fmt.Printf("⚠️  存储过程已存在，跳过: %v\n", err)
				continue
			}
			fmt.Printf("❌ 执行失败: %v\n", err)
			fmt.Printf("   SQL: %s\n", stmt)
		} else {
			fmt.Printf("✅ 执行成功\n")
		}
	}

	fmt.Println("\n🎉 迁移脚本执行完成")
}

// splitSQLStatements 分割SQL语句，处理存储过程中的分号
func splitSQLStatements(content string) []string {
	var statements []string
	var current strings.Builder
	inDelimiter := false
	
	lines := strings.Split(content, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// 跳过注释行
		if strings.HasPrefix(line, "--") || line == "" {
			continue
		}
		
		// 检查DELIMITER语句
		if strings.HasPrefix(line, "DELIMITER") {
			if strings.Contains(line, "//") {
				inDelimiter = true
			} else if strings.Contains(line, ";") {
				inDelimiter = false
			}
			continue
		}
		
		current.WriteString(line)
		current.WriteString("\n")
		
		// 如果在存储过程中，遇到//才结束
		if inDelimiter {
			if strings.HasSuffix(line, "//") {
				stmt := strings.TrimSpace(current.String())
				stmt = strings.TrimSuffix(stmt, "//")
				if stmt != "" {
					statements = append(statements, stmt)
				}
				current.Reset()
			}
		} else {
			// 普通SQL语句，遇到分号结束
			if strings.HasSuffix(line, ";") {
				stmt := strings.TrimSpace(current.String())
				if stmt != "" {
					statements = append(statements, stmt)
				}
				current.Reset()
			}
		}
	}
	
	// 处理最后一个语句
	if current.Len() > 0 {
		stmt := strings.TrimSpace(current.String())
		if stmt != "" {
			statements = append(statements, stmt)
		}
	}
	
	return statements
}

// truncateSQL 截断SQL语句用于显示
func truncateSQL(sql string, maxLen int) string {
	sql = strings.ReplaceAll(sql, "\n", " ")
	sql = strings.ReplaceAll(sql, "\t", " ")
	// 压缩多个空格为一个
	for strings.Contains(sql, "  ") {
		sql = strings.ReplaceAll(sql, "  ", " ")
	}
	
	if len(sql) <= maxLen {
		return sql
	}
	return sql[:maxLen] + "..."
}
